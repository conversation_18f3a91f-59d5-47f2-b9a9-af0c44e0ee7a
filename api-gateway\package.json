{"name": "apigateway", "version": "1.0.0", "description": "Demo of API Gate for Micro Services", "main": "server.ts", "//type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "SET NODE_ENV=local & nodemon --watch src/** --ext ts,json --ignore src/**/*.spec.ts --exec ts-node --files server.ts", "start-dev": "NODE_ENV=development nodemon --watch ts-node server.ts", "start-prod": "NODE_ENV=production nodemon --watch ts-node --files server.ts", "pm2": "NODE_ENV=production pm2 start server.ts --watch", "lint": "eslint .", "build": "tsc --build --force tsconfig.json && cp -r src/resources build/src/"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.1.0", "eslint": "^8.41.0", "express": "^4.18.2", "express-http-proxy": "^1.6.3", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.0", "mongoose": "^7.6.10", "multer": "^1.4.5-lts.1", "nodemon": "^3.0.1", "redis": "^4.6.6", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "ts-node": "^10.9.1", "typescript": "^5.0.4", "validatorjs": "^3.22.1", "zlib": "^1.0.5"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.17", "@types/express-http-proxy": "^1.6.3", "@types/i18n": "^0.13.6", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.11", "@types/redis": "^4.0.11", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/validatorjs": "^3.15.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8"}}