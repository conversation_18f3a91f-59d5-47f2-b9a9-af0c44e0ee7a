/**
 * ProductSlice PROPRE - Mobile E-Luxe 1.0
 * EXACTEMENT comme le client web - AUCUNE donnée mockée
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../../services/ApiService';

// State interface SIMPLE et PROPRE
interface ProductState {
  // Produits
  products: any[];
  currentProduct: any | null;
  bestSellingProducts: any[];
  showingProducts: any[];
  
  // Catégories
  categories: any[];
  showingCategories: any[];
  
  // Pagination
  pagination: {
    page: number;
    totalPages: number;
    total: number;
    limit: number;
  };
  
  // États
  isLoading: boolean;
  error: string | null;
  
  // Filtres
  filter: {
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    page?: number;
    limit?: number;
  };
}

const initialState: ProductState = {
  products: [],
  currentProduct: null,
  bestSellingProducts: [],
  showingProducts: [],
  categories: [],
  showingCategories: [],
  pagination: {
    page: 1,
    totalPages: 1,
    total: 0,
    limit: 48,
  },
  isLoading: false,
  error: null,
  filter: {},
};

// ===== ACTIONS ASYNC PROPRES =====

/**
 * Récupère les produits avec pagination et filtres
 */
export const getProductsAsync = createAsyncThunk(
  'product/getProducts',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await apiService.getProducts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

/**
 * Récupère un produit par ID
 */
export const getProductByIdAsync = createAsyncThunk(
  'product/getProductById',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.getProductById(productId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch product');
    }
  }
);

/**
 * Récupère les meilleurs vendeurs
 */
export const getBestSellersAsync = createAsyncThunk(
  'product/getBestSellers',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.getBestSellers();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch best sellers');
    }
  }
);

/**
 * Récupère les produits affichés
 */
export const getShowingProductsAsync = createAsyncThunk(
  'product/getShowingProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.getShowingProducts();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch showing products');
    }
  }
);

/**
 * Récupère les catégories
 */
export const getCategoriesAsync = createAsyncThunk(
  'product/getCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.getCategories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

/**
 * Récupère les catégories affichées
 */
export const getShowingCategoriesAsync = createAsyncThunk(
  'product/getShowingCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.getShowingCategories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch showing categories');
    }
  }
);

// ===== SLICE PROPRE =====

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<any>) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    clearFilter: (state) => {
      state.filter = {};
    },
    clearProducts: (state) => {
      state.products = [];
      state.pagination = initialState.pagination;
    },
    setCurrentProduct: (state, action: PayloadAction<any>) => {
      state.currentProduct = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Get products
    builder
      .addCase(getProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload.products || action.payload;
        state.pagination = {
          page: action.payload.page || 1,
          totalPages: action.payload.totalPages || 1,
          total: action.payload.total || 0,
          limit: action.payload.limit || 48,
        };
      })
      .addCase(getProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get product by ID
    builder
      .addCase(getProductByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProduct = action.payload;
      })
      .addCase(getProductByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get best sellers
    builder
      .addCase(getBestSellersAsync.fulfilled, (state, action) => {
        state.bestSellingProducts = action.payload;
      })

    // Get showing products
    builder
      .addCase(getShowingProductsAsync.fulfilled, (state, action) => {
        state.showingProducts = action.payload;
      })

    // Get categories
    builder
      .addCase(getCategoriesAsync.fulfilled, (state, action) => {
        state.categories = action.payload;
      })

    // Get showing categories
    builder
      .addCase(getShowingCategoriesAsync.fulfilled, (state, action) => {
        state.showingCategories = action.payload;
      });
  },
});

export const { setFilter, clearFilter, clearProducts, setCurrentProduct } = productSlice.actions;
export default productSlice.reducer;
