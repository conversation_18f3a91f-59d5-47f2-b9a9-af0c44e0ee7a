import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, Category, ProductFilter } from '../../../domain/entities/Product';
import { EcommerceApiService } from '../../../services/EcommerceApiService';

// State interface
interface ProductState {
  products: Product[];
  categories: Category[];
  featuredProducts: Product[];
  bestSellingProducts: Product[];
  newArrivals: Product[];
  currentProduct: Product | null;
  isLoading: boolean;
  error: string | null;
  filter: ProductFilter;
  pagination: {
    page: number;
    totalPages: number;
    total: number;
  };
}

// Initial state
const initialState: ProductState = {
  products: [],
  categories: [],
  featuredProducts: [],
  bestSellingProducts: [],
  newArrivals: [],
  currentProduct: null,
  isLoading: false,
  error: null,
  filter: {
    page: 1,
    limit: 20,
    sortBy: 'newest',
  },
  pagination: {
    page: 1,
    totalPages: 1,
    total: 0,
  },
};

// Instance du service API
const ecommerceService = new EcommerceApiService();

// Async thunks avec vrais appels API
export const getProductsAsync = createAsyncThunk(
  'product/getProducts',
  async (filter?: ProductFilter, { rejectWithValue }) => {
    try {
      console.log('🛒 Récupération des produits via API...', filter);

      // Appel API uniquement - pas de données mockées
      const response = await ecommerceService.getProducts({
        page: filter?.page || 1,
        limit: filter?.limit || 12,
        category: filter?.category,
        search: filter?.search,
        minPrice: filter?.minPrice,
        maxPrice: filter?.maxPrice,
        sortBy: filter?.sortBy as any,
        sortOrder: filter?.sortOrder as any,
      });

      console.log('✅ Produits récupérés depuis API:', response.data?.products?.length || 0);
      return response.data;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des produits:', error);
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

export const getProductByIdAsync = createAsyncThunk(
  'product/getProductById',
  async (productId: string, { rejectWithValue }) => {
    try {
      console.log('🔍 Récupération du produit:', productId);

      const product = await ecommerceService.getProductById(productId);

      console.log('✅ Produit récupéré:', product?.title || product?.name);
      return product;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération du produit:', error);
      return rejectWithValue(error.message || 'Failed to fetch product');
    }
  }
);

export const getCategoriesAsync = createAsyncThunk(
  'product/getCategories',
  async (_, { rejectWithValue }) => {
    try {
      console.log('📂 Récupération des catégories...');

      const response = await ecommerceService.getCategories();

      console.log('✅ Catégories récupérées:', response.data?.length || 0);
      return response.data;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des catégories:', error);
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

export const getFeaturedProductsAsync = createAsyncThunk(
  'product/getFeaturedProducts',
  async (limit?: number, { rejectWithValue }) => {
    try {
      console.log('⭐ Récupération des produits en vedette...');

      const response = await ecommerceService.getFeaturedProducts();
      const products = response.data || [];

      // Limiter le nombre de produits si spécifié
      const limitedProducts = limit ? products.slice(0, limit) : products;

      console.log('✅ Produits en vedette récupérés:', limitedProducts.length);
      return limitedProducts;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des produits en vedette:', error);
      return rejectWithValue(error.message || 'Failed to fetch featured products');
    }
  }
);

/**
 * Récupère les catégories affichées (showing) - comme le client web
 */
export const getShowingCategoriesAsync = createAsyncThunk(
  'product/getShowingCategories',
  async (lang: string = 'en', { rejectWithValue }) => {
    try {
      console.log('📂 Récupération des catégories affichées - EXACTEMENT comme CategoryServices.getAllCategory...');

      // Appel EXACTEMENT comme le client web
      const response = await ecommerceService.getShowingCategories();

      // Extraction des données comme le client web
      const categories = response.data || response;
      console.log('✅ Catégories affichées récupérées (logique client web):', categories?.length || 0);

      return categories;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des catégories affichées:', error);

      // Rejeter l'erreur - pas de données mockées
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

/**
 * Récupère les catégories avec produits pour la page d'accueil - comme le client web
 */
export const getShowingProductsOnHomePageCategoriesAsync = createAsyncThunk(
  'product/getShowingProductsOnHomePageCategories',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🏠 Récupération des catégories avec produits pour la page d\'accueil...');

      const response = await ecommerceService.getShowingProductsOnHomePageCategories();

      console.log('✅ Catégories avec produits récupérées:', response.data?.length || 0);
      return response.data;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des catégories avec produits:', error);
      return rejectWithValue(error.message || 'Failed to fetch categories with products');
    }
  }
);

/**
 * Récupère les meilleurs vendeurs du mois - comme le client web
 */
export const getBestSellersOfMonthAsync = createAsyncThunk(
  'product/getBestSellersOfMonth',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🏆 Récupération des meilleurs vendeurs - EXACTEMENT comme ProductServices.getAllProducts...');

      // Appel EXACTEMENT comme le client web
      const response = await ecommerceService.getBestSellersOfMonth();

      // Extraction des données comme le client web
      const products = response.data || response;
      console.log('✅ Meilleurs vendeurs récupérés (logique client web):', products?.length || 0);

      return products;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des meilleurs vendeurs:', error);

      // Rejeter l'erreur - pas de données mockées
      return rejectWithValue(error.message || 'Failed to fetch best sellers');
    }
  }
);

/**
 * Récupère les nouveaux arrivages - comme le client web
 */
export const getNewArrivalsAsync = createAsyncThunk(
  'product/getNewArrivals',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🆕 Récupération des nouveaux arrivages...');

      const response = await ecommerceService.getNewArrivals();

      console.log('✅ Nouveaux arrivages récupérés:', response.data?.length || 0);
      return response.data;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des nouveaux arrivages:', error);
      return rejectWithValue(error.message || 'Failed to fetch new arrivals');
    }
  }
);

// Ces méthodes sont déjà définies plus haut, suppression des duplications

export const searchProductsAsync = createAsyncThunk(
  'product/searchProducts',
  async ({ query, filter }: { query: string; filter?: ProductFilter }, { rejectWithValue }) => {
    try {
      console.log('🔍 Recherche de produits:', query);

      const response = await ecommerceService.searchProducts(query, filter);
      const data = response.data;

      console.log('✅ Résultats de recherche:', data?.products?.length || 0);
      return {
        products: data?.products || [],
        total: data?.total || 0,
        page: filter?.page || 1,
        totalPages: Math.ceil((data?.total || 0) / (filter?.limit || 12)),
      };
    } catch (error: any) {
      console.error('❌ Erreur lors de la recherche de produits:', error);
      return rejectWithValue(error.message || 'Failed to search products');
    }
  }
);

// Slice
const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilter: (state, action: PayloadAction<Partial<ProductFilter>>) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    clearProducts: (state) => {
      state.products = [];
      state.pagination = {
        page: 1,
        totalPages: 1,
        total: 0,
      };
    },
    setCurrentProduct: (state, action: PayloadAction<Product | null>) => {
      state.currentProduct = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Get products
    builder
      .addCase(getProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload?.products || [];
        state.pagination = {
          page: action.payload?.page || 1,
          totalPages: action.payload?.totalPages || 1,
          total: action.payload?.total || 0,
        };
        state.error = null;
      })
      .addCase(getProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Get product by ID
    builder
      .addCase(getProductByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProduct = action.payload;
        state.error = null;
      })
      .addCase(getProductByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Get categories
    builder
      .addCase(getCategoriesAsync.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      .addCase(getCategoriesAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Get featured products
    builder
      .addCase(getFeaturedProductsAsync.fulfilled, (state, action) => {
        state.featuredProducts = action.payload;
      })
      .addCase(getFeaturedProductsAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Get best selling products (getBestSellersOfMonthAsync)
    builder
      .addCase(getBestSellersOfMonthAsync.fulfilled, (state, action) => {
        state.bestSellingProducts = action.payload;
      })
      .addCase(getBestSellersOfMonthAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Get showing categories
    builder
      .addCase(getShowingCategoriesAsync.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      .addCase(getShowingCategoriesAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Get showing products on home page categories
    builder
      .addCase(getShowingProductsOnHomePageCategoriesAsync.fulfilled, (state, action) => {
        // Ces catégories peuvent être stockées séparément si nécessaire
        console.log('✅ Catégories avec produits pour la page d\'accueil chargées');
      })
      .addCase(getShowingProductsOnHomePageCategoriesAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Get new arrivals
    builder
      .addCase(getNewArrivalsAsync.fulfilled, (state, action) => {
        state.newArrivals = action.payload;
      })
      .addCase(getNewArrivalsAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Search products
    builder
      .addCase(searchProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload.products;
        state.pagination = {
          page: action.payload.page,
          totalPages: action.payload.totalPages,
          total: action.payload.total,
        };
        state.error = null;
      })
      .addCase(searchProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setFilter, clearProducts, setCurrentProduct } = productSlice.actions;
export default productSlice.reducer;
