import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import jwtUtilities from "../utils/jwt-utilities.util";
import customResponse from "../utils/custom-response.util";
import errorNumbers from "../utils/error-numbers.util";
import statusCode from "../utils/status-code.util";
import config from "../../config";

const blogServiceProxy = httpProxy(config.blogServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-09-05
 *
 * Class BlogServiceRoutes
 */
class BlogServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-09-05
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all blogs service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-09-05
   *
   * @returns {Router} of blog service
   */
  public blogServiceRoutes(): Router {
    return this.router.use(
      "/blog",
      routesGrouping.group((router) => {
        // All post routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/posts",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.get("/showing/store", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.delete("/:postIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/post",
              routesGrouping.group((router) => {
                router.get("/:postId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.get("/slug/:slug", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.post("/:postId/reviews", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.put("/:postId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.patch("/:postId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.get(
                  "/:postId/category/:categoryId/assign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    blogServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:postId/category/:categoryId/unassign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    blogServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:postId/tag/:tagId/assign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    blogServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:postId/tag/:tagId/unassign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    blogServiceProxy(req, res, next);
                  }
                );

                router.delete("/:postId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All category routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/categories",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.get("/all", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.delete("/:categoryIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/category",
              routesGrouping.group((router) => {
                router.get("/:categoryId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.put("/:categoryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.put("/:categoryId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.delete("/:categoryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All tag routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/tags",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });

                router.delete("/:tagIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  blogServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/tag",
              routesGrouping.group((router) => {
                router.get("/:tagId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.put("/:tagId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.put("/:tagId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });

                router.delete("/:tagId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  blogServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const blogServiceRoutes = new BlogServiceRoutes();
export default blogServiceRoutes;
