import mongoose from "mongoose";

const emailSchema = new mongoose.Schema(
  {
    message_id: { type: String, required: false },
    sender_name: { type: String, required: false },
    subject: { type: String, required: true },
    body: { type: String, required: true },
    accepted: [{ type: String, required: false }],
    rejected: [{ type: String, required: false }],
    envelope_time: { type: Number, required: false },
    message_time: { type: Number, required: false },
    message_size: { type: Number, required: false },
    response: { type: String, required: false },
    envelope: {
      from: { type: String, required: false },
      to: [{ type: String, required: false }],
    },
  },
  {
    timestamps: {
      createdAt: "created_at", // Use `created_at` to store the created date
      updatedAt: "updated_at", // and `updated_at` to store the last updated date
    },
  }
);

const Email = mongoose.model("email", emailSchema);

export default Email;
