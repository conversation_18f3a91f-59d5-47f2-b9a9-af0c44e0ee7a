# Server config
NODE_SERVER_PORT=2200
NODE_SERVER_HOST=localhost
NODE_SERVER_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApyKQ4/xUF3U8OUP6QKES\nMHQxSeUmxlAasCdLQBHU6W8X4Uyg7CvMxzkW60mBbtXVDcnunLYQkVSXerfDgvwp\nc9QgRly/4DfHBmM51TCL0EPOHYubiSg35ouf3+N2B/0T0vIz5NY4Bo+Rs93RKPfR\nvq1FyUpIVWNy3wThXsKxmWUhS+qZ4sC6qNlB4bwkcLiXvdI1iIzaDuhopbUksfHc\nmudNuqD7mCfsXbE5VG2T+m9L97iYW/BKTUIhzBo/mss49oRcBd4R4+kLTtFGdu2i\nRndYAsIjSOyQ7IPwmHd8wuhtnlyDRYOTSCtFNKMWkDBSa0vEGHKyVZwmM4FMXPwB\nxQIDAQAB\n-----END PUBLIC KEY-----

# Redis db
REDIS_DB_HOST=127.0.0.1
REDIS_DB_PORT=6379
REDIS_DB_USER=valentin
REDIS_DB_PASSWORD=phpuser
REDIS_DB_NAME=phpuser

# Mongoose db
MONGODB_DB_HOST=127.0.0.1
MONGODB_DB_PORT=27017
MONGODB_DB_USER=valentin
MONGODB_DB_PASSWORD=password
MONGODB_DB_NAME=el_emails_db

# Rabbitmq db
RABBITMQ_DB_HOST=127.0.0.1
RABBITMQ_DB_PORT=27017
RABBITMQ_DB_USER=valentin
RABBITMQ_DB_PASSWORD=password
RABBITMQ_DB_NAME=el_emails_db

# Mail config
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.postmarkapp.com
# MAIL_PORT=587
# MAIL_USERNAME=************************************
# MAIL_PASSWORD=************************************
# MAIL_ENCRYPTION=tls
# MAIL_FROM_NAME="E.LUXE LLC"
MAIL_MAILER=smtp
MAIL_HOST=smtp.office365.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=Eluxe@2024!
MAIL_ENCRYPTION=tls
MAIL_FROM_NAME="E.LUXE LLC"

# Rabbitmq db
RABBITMQ_DB_HOST=127.0.0.1
RABBITMQ_DB_PORT=15672
RABBITMQ_DB_USER=valentin
RABBITMQ_DB_PASSWORD=password
RABBITMQ_DB_NAME=el_emails_db

# Swagger documentation
SWAGGER_BASE_URL=/v1/emails/docs

# Postmark Server API tokens
POSTMARK_API_TOKEN=
