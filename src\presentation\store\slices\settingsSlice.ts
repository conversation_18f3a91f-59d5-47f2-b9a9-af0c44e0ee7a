/**
 * Redux Slice pour Settings - Mobile E-Luxe 1.0
 * Synchronisé avec le client web E-Luxe
 * Gère l'état des paramètres globaux et de personnalisation
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ecommerceService } from '../../../services/EcommerceApiService';

// Interfaces pour les types de données (comme le client web)
interface GlobalSetting {
  shop_name?: string;
  shop_description?: string;
  shop_address?: string;
  shop_phone?: string;
  shop_email?: string;
  currency?: string;
  language?: string;
}

interface SliderSetting {
  left_right_arrow?: boolean;
  bottom_dots?: boolean;
  both_slider?: boolean;
  first_img: string;
  first_title: Record<string, string>;
  first_description: Record<string, string>;
  first_button: Record<string, string>;
  first_link?: string;
  second_img: string;
  second_title: Record<string, string>;
  second_description: Record<string, string>;
  second_button: Record<string, string>;
  second_link?: string;
  third_img?: string;
  third_title?: Record<string, string>;
  third_description?: Record<string, string>;
  third_button?: Record<string, string>;
  third_link?: string;
}

// Interface pour les services (comme le client web)
interface HomeServiceSetting {
  service_one_status: boolean;
  service_one_title: Record<string, string>; // Multilingue {en, fr}
  service_one_description: Record<string, string>; // Multilingue {en, fr}
  service_one_image: string;

  service_two_status: boolean;
  service_two_title: Record<string, string>; // Multilingue {en, fr}
  service_two_description: Record<string, string>; // Multilingue {en, fr}
  service_two_image: string;

  service_three_status: boolean;
  service_three_title: Record<string, string>; // Multilingue {en, fr}
  service_three_description: Record<string, string>; // Multilingue {en, fr}
  service_three_image: string;

  service_four_status: boolean;
  service_four_title: Record<string, string>; // Multilingue {en, fr}
  service_four_description: Record<string, string>; // Multilingue {en, fr}
  service_four_image: string;
}

// Interface pour les paramètres home (comme le client web)
interface HomeSetting extends HomeServiceSetting {
  coupon_status?: boolean;
  featured_status?: boolean;
  discount_status?: boolean;
  daily_needs_status?: boolean;
  slider_width_status?: boolean;
  promotion_banner_status?: boolean;
  delivery_status?: boolean;
  popular_products_status?: boolean;
  discount_product_status?: boolean;
  discount_coupon_code?: string[];
  place_holder_img?: string;
  feature_promo_status?: boolean;
  quick_delivery_link?: string;
  quick_delivery_img?: string;
  discount_title?: Record<string, string>;
  daily_need_title?: Record<string, string>;
  daily_need_description?: Record<string, string>;
  daily_need_app_link?: string;
  daily_need_google_link?: string;
  daily_need_img_left?: string;
  daily_need_img_right?: string;
  button1_img?: string;
  button2_img?: string;
}

interface StoreCustomizationSetting {
  navbar?: {
    logo?: string;
  };
  slider?: SliderSetting;
  home?: HomeSetting; // Maintenant typé correctement
}

interface SettingsResponse {
  globalSetting: GlobalSetting;
  storeCustomizationSetting: StoreCustomizationSetting;
}

// État initial
interface SettingsState {
  globalSetting: GlobalSetting | null;
  storeCustomizationSetting: StoreCustomizationSetting | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

const initialState: SettingsState = {
  globalSetting: null,
  storeCustomizationSetting: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// ===== ASYNC THUNKS =====

/**
 * Récupère les paramètres globaux - comme le client web
 */
export const fetchGlobalSettingsAsync = createAsyncThunk(
  'settings/fetchGlobalSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getGlobalSettings();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch global settings');
    }
  }
);

/**
 * Récupère les paramètres de personnalisation - comme le client web
 */
export const fetchStoreCustomizationSettingsAsync = createAsyncThunk(
  'settings/fetchStoreCustomizationSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getStoreCustomizationSettings();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch store customization settings');
    }
  }
);

/**
 * Récupère tous les paramètres en une seule fois - comme le client web
 */
export const fetchAllSettingsAsync = createAsyncThunk(
  'settings/fetchAllSettings',
  async (_, { rejectWithValue }) => {
    try {
      const [globalSetting, storeCustomizationSetting] = await Promise.all([
        ecommerceService.getGlobalSettings(),
        ecommerceService.getStoreCustomizationSettings(),
      ]);

      return {
        globalSetting,
        storeCustomizationSetting,
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch all settings');
    }
  }
);



// ===== SLICE =====

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Actions synchrones
    clearError: (state) => {
      state.error = null;
    },
    
    resetSettings: (state) => {
      state.globalSetting = null;
      state.storeCustomizationSetting = null;
      state.error = null;
      state.lastUpdated = null;
    },

    // Action pour mettre à jour manuellement les paramètres
    updateGlobalSetting: (state, action: PayloadAction<Partial<GlobalSetting>>) => {
      if (state.globalSetting) {
        state.globalSetting = { ...state.globalSetting, ...action.payload };
      }
    },

    updateStoreCustomizationSetting: (state, action: PayloadAction<Partial<StoreCustomizationSetting>>) => {
      if (state.storeCustomizationSetting) {
        state.storeCustomizationSetting = { ...state.storeCustomizationSetting, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    // fetchGlobalSettingsAsync
    builder
      .addCase(fetchGlobalSettingsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchGlobalSettingsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.globalSetting = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchGlobalSettingsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // fetchStoreCustomizationSettingsAsync
    builder
      .addCase(fetchStoreCustomizationSettingsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStoreCustomizationSettingsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.storeCustomizationSetting = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchStoreCustomizationSettingsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // fetchAllSettingsAsync
    builder
      .addCase(fetchAllSettingsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllSettingsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.globalSetting = action.payload.globalSetting;
        state.storeCustomizationSetting = action.payload.storeCustomizationSetting;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchAllSettingsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });


  },
});

// Export des actions
export const {
  clearError,
  resetSettings,
  updateGlobalSetting,
  updateStoreCustomizationSetting,
} = settingsSlice.actions;

// Export du reducer
export default settingsSlice.reducer;

// Sélecteurs (EXACTEMENT comme le client web)
export const selectGlobalSetting = (state: { setting: SettingsState }) => state.setting.globalSetting;
export const selectStoreCustomizationSetting = (state: { setting: SettingsState }) => state.setting.storeCustomizationSetting;
export const selectSettingsLoading = (state: { setting: SettingsState }) => state.setting.isLoading;
export const selectSettingsError = (state: { setting: SettingsState }) => state.setting.error;
export const selectLogo = (state: { setting: SettingsState }) => state.setting.storeCustomizationSetting?.navbar?.logo;
export const selectSliderSettings = (state: { setting: SettingsState }) => state.setting.storeCustomizationSetting?.slider;

// Alias pour compatibilité avec HomeScreen
export const getAllSettingsAsync = fetchAllSettingsAsync;
