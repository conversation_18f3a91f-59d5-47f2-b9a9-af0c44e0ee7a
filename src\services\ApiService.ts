/**
 * Service API Unifié - Mobile E-Luxe 1.0
 * EXACTEMENT comme le client web - AUCUNE donnée mockée
 */

import { apiClient } from '../data/datasources/ApiClient';

class ApiService {
  
  // ===== PRODUITS =====
  
  /**
   * Récupère tous les produits avec pagination et filtres
   * EXACTEMENT comme le client web
   */
  async getProducts(params: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: string;
  } = {}) {
    console.log('📦 ApiService.getProducts:', params);
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/products', undefined, {
      params: {
        page: params.page || 1,
        limit: params.limit || 48,
        ...params
      }
    });
    
    console.log('✅ Produits récupérés:', response);
    return response;
  }

  /**
   * Récupère un produit par ID
   */
  async getProductById(productId: string) {
    console.log('🔍 ApiService.getProductById:', productId);
    
    const response = await apiClient.getWithLang<any>(`/v1/{lang}/product/${productId}`);
    
    console.log('✅ Produit récupéré:', response);
    return response;
  }

  /**
   * Récupère les produits affichés sur la page d'accueil
   */
  async getShowingProducts() {
    console.log('🏠 ApiService.getShowingProducts');
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/products/showing');
    
    console.log('✅ Produits showing récupérés:', response);
    return response;
  }

  /**
   * Récupère les meilleurs vendeurs
   */
  async getBestSellers() {
    console.log('🏆 ApiService.getBestSellers');
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/products/best-sellers');
    
    console.log('✅ Best sellers récupérés:', response);
    return response;
  }

  // ===== CATÉGORIES =====
  
  /**
   * Récupère toutes les catégories
   */
  async getCategories() {
    console.log('📂 ApiService.getCategories');
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/categories');
    
    console.log('✅ Catégories récupérées:', response);
    return response;
  }

  /**
   * Récupère les catégories affichées
   */
  async getShowingCategories() {
    console.log('📂 ApiService.getShowingCategories');
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/categories/showing');
    
    console.log('✅ Catégories showing récupérées:', response);
    return response;
  }

  // ===== PANIER =====
  
  /**
   * Récupère le panier
   */
  async getCart() {
    console.log('🛒 ApiService.getCart');
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/cart');
    
    console.log('✅ Panier récupéré:', response);
    return response;
  }

  /**
   * Ajoute un produit au panier
   */
  async addToCart(productId: string, quantity: number, variantId?: string) {
    console.log('➕ ApiService.addToCart:', { productId, quantity, variantId });
    
    const response = await apiClient.postWithLang<any>('/v1/{lang}/cart/add', {
      productId,
      quantity,
      variantId
    });
    
    console.log('✅ Produit ajouté au panier:', response);
    return response;
  }

  // ===== COMMANDES =====
  
  /**
   * Crée une commande
   */
  async createOrder(orderData: any) {
    console.log('🛒 ApiService.createOrder:', orderData);
    
    const response = await apiClient.postWithLang<any>('/v1/{lang}/orders', orderData);
    
    console.log('✅ Commande créée:', response);
    return response;
  }

  /**
   * Récupère les commandes
   */
  async getOrders(page: number = 1, limit: number = 10) {
    console.log('📋 ApiService.getOrders:', { page, limit });
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/orders', undefined, {
      params: { page, limit }
    });
    
    console.log('✅ Commandes récupérées:', response);
    return response;
  }

  // ===== AUTHENTIFICATION =====
  
  /**
   * Connexion utilisateur
   */
  async login(email: string, password: string) {
    console.log('🔐 ApiService.login:', email);
    
    const response = await apiClient.postWithLang<any>('/v1/{lang}/auth/login', {
      email,
      password
    });
    
    console.log('✅ Connexion réussie:', response);
    return response;
  }

  /**
   * Inscription utilisateur
   */
  async register(userData: any) {
    console.log('📝 ApiService.register:', userData);
    
    const response = await apiClient.postWithLang<any>('/v1/{lang}/auth/register', userData);
    
    console.log('✅ Inscription réussie:', response);
    return response;
  }

  // ===== SETTINGS =====
  
  /**
   * Récupère tous les settings
   */
  async getAllSettings() {
    console.log('⚙️ ApiService.getAllSettings');
    
    const response = await apiClient.getWithLang<any>('/v1/{lang}/settings');
    
    console.log('✅ Settings récupérés:', response);
    return response;
  }
}

// Instance unique
export const apiService = new ApiService();
export default apiService;
