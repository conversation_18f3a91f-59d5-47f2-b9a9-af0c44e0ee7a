import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import jwtUtilities from "../utils/jwt-utilities.util";
import customResponse from "../utils/custom-response.util";
import errorNumbers from "../utils/error-numbers.util";
import statusCode from "../utils/status-code.util";
import config from "../../config";

const schoolProgramServiceProxy = httpProxy(config.schoolProgramServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-10-24
 *
 * Class SchoolProgramServiceRoutes
 */
class SchoolProgramServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-10-24
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all school programs service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-10-24
   *
   * @returns {Router} of product service
   */
  public schoolProgramServiceRoutes(): Router {
    return this.router.use(
      "/school-program",
      routesGrouping.group((router) => {
        // All textbooks routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/textbooks",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  schoolProgramServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  schoolProgramServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/textbook",
              routesGrouping.group((router) => {
                router.get("/:textbookId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  schoolProgramServiceProxy(req, res, next);
                });

                router.put("/:textbookId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  schoolProgramServiceProxy(req, res, next);
                });

                router.patch("/:textbookId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  schoolProgramServiceProxy(req, res, next);
                });

                router.delete("/:textbookId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  schoolProgramServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const schoolProgramServiceRoutes = new SchoolProgramServiceRoutes();
export default schoolProgramServiceRoutes;
