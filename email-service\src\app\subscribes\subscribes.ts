import emailSubscribe from "../modules/email/email.subscribe";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-25
 *
 * Class Subscribes
 */
class Subscribes {
  /**
   * Creating app Subscribes starts
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-25
   *
   * @returns {void}
   */
  public appSubscribes(): void {
    // Includes all subscribes
    emailSubscribe.sendEmail();
  }

  /**
   * Load routes
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-03-23
   *
   * @returns {void}
   */
  public subscribesConfig(): void {
    this.appSubscribes();
  }
}

export default Subscribes;
