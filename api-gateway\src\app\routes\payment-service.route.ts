import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import customResponse from "../utils/custom-response.util";
import errorNumbers from "../utils/error-numbers.util";
import statusCode from "../utils/status-code.util";
import config from "../../config";
import jwtUtilities from "../utils/jwt-utilities.util";

const paymentServiceProxy = httpProxy(config.paymentServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-08-13
 *
 * Class PaymentServiceRoutes
 */
class PaymentServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-13
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all payment service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-13
   *
   * @returns {Router} of payment service
   */
  public paymentServiceRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All payment method routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/paymentMethods",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/system/:systemId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/paymentMethod",
              routesGrouping.group((router) => {
                router.get("/:paymentMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.get("/slug/:paymentMethodSlug", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.get(
                  "/:paymentMethodId/system/:systemId/assign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    paymentServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:paymentMethodId/system/:systemId/unassign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    paymentServiceProxy(req, res, next);
                  }
                );

                router.put("/:paymentMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.delete("/:paymentMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All system routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/systems",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/system",
              routesGrouping.group((router) => {
                router.get("/:systemId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.put("/:systemId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.delete("/:systemId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All stripe customer routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/stripeCustomers",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/user/:userId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/stripeCustomer",
              routesGrouping.group((router) => {
                router.get("/:stripeCustomerId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                // router.get("/", (req, res, next) => {
                //   // Update url with original url which contain all path
                //   req.url = req.originalUrl;

                //   paymentServiceProxy(req, res, next);
                // });

                router.put("/:stripeCustomerId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.delete("/:stripeCustomerId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All stripe payment methods routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/stripePaymentMethods",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get(
                  "/stripeCustomer/:stripeCustomerId",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    paymentServiceProxy(req, res, next);
                  }
                );
              })
            );

            router.use(
              "/stripePaymentMethod",
              routesGrouping.group((router) => {
                router.get("/:stripePaymentMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.get(
                  "/:stripePaymentMethodId/stripeCustomer/:stripeCustomerId/attach",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;

                    paymentServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:stripePaymentMethodId/stripeCustomer/detach",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;

                    paymentServiceProxy(req, res, next);
                  }
                );

                router.put("/:stripePaymentMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All stripe payment intents routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/stripePaymentIntents",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get(
                  "/stripeCustomer/:stripeCustomerId",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    paymentServiceProxy(req, res, next);
                  }
                );
              })
            );

            router.use(
              "/stripePaymentIntent",
              routesGrouping.group((router) => {
                router.get("/:stripePaymentIntentId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });

                router.post(
                  "/:stripePaymentIntentId/confirm",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;

                    paymentServiceProxy(req, res, next);
                  }
                );

                router.post(
                  "/:stripePaymentIntentId/cancel",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;

                    paymentServiceProxy(req, res, next);
                  }
                );

                router.put("/:stripePaymentIntentId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All paypal orders routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/paypalOrders",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  paymentServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/paypalOrder",
              routesGrouping.group((router) => {
                // router.get("/:stripePaymentIntentId", (req, res, next) => {
                //   // Update url with original url which contain all path
                //   req.url = req.originalUrl;
                //   paymentServiceProxy(req, res, next);
                // });

                // router.post(
                //   "/:stripePaymentIntentId/confirm",
                //   (req, res, next) => {
                //     // Update url with original url which contain all path
                //     req.url = req.originalUrl;

                //     paymentServiceProxy(req, res, next);
                //   }
                // );

                // router.post(
                //   "/:stripePaymentIntentId/cancel",
                //   (req, res, next) => {
                //     // Update url with original url which contain all path
                //     req.url = req.originalUrl;

                //     paymentServiceProxy(req, res, next);
                //   }
                // );

                router.post("/:paypalOrderId/capture", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  paymentServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const paymentServiceRoutes = new PaymentServiceRoutes();
export default paymentServiceRoutes;
