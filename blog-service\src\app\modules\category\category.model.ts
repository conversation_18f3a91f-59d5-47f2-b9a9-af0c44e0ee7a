import mongoose from "mongoose";

const categoryShema = new mongoose.Schema(
  {
    name: { type: Object, require: true },
    slug: { type: String, require: false },
    description: { type: Object, required: false },
    parent_id: { type: String, required: false },
    parent_name: { type: String, required: false },
    id: { type: String, required: false },
    icon: { type: String, required: false },
    status: { type: String, lowercase: true, enum: ['show', 'hide'], default: 'show' },
    posts: [{ type: mongoose.Schema.Types.ObjectId, ref: "post" }],
  },
  {
    timestamps: {
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
  }
);

const Category = mongoose.model("category", categoryShema);

export default Category;
