import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import jwtUtilities from "../utils/jwt-utilities.util";
import statusCode from "../utils/status-code.util";
import errorNumbers from "../utils/error-numbers.util";
import customResponse from "../utils/custom-response.util";
import config from "../../config";

const settingServiceProxy = httpProxy(config.settingServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2024-07-14
 *
 * Class SettingServiceRoutes
 */
class SettingServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-14
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all settings service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-14
   *
   * @returns {Router} of setting service
   */
  public settingServiceRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All language routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/languages",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/update/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/:languageIds", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/language",
              routesGrouping.group((router) => {
                router.get("/:langageId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/:languageId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/:languageId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/:languageId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All currency routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/currencies",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/:currencyIds", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/currency",
              routesGrouping.group((router) => {
                router.get("/:currencyId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/:currencyId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/:currencyId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/:currencyId/live-exchange-rates", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/:currencyId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All settings routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/settings",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/setting",
              routesGrouping.group((router) => {
                router.get("/global", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/store-setting", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/store/seo", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/store/customization", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/global", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/store-setting", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/store/customization", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All notifications routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/notifications",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/:notificationIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/notification",
              routesGrouping.group((router) => {
                router.get("/:notificationId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.put("/:notificationId/status", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/:notificationId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });

                router.delete("/product/productId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  settingServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const settingServiceRoutes = new SettingServiceRoutes();
export default settingServiceRoutes;
