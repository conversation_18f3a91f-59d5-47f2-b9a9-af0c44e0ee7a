/**
 * Composant ProductSlides - Mobile E-Luxe 1.0
 * Reproduction exacte du composant ProductSlides.tsx du client web
 * Affiche les catégories avec leurs produits dynamiquement
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { ecommerceApi } from '../../services/EcommerceApiService';
import ProductCard from './ProductCard';
import { ELuxeColors, ComponentColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import { ELuxeIcon } from '../../components/common/Icon';
import { getCategoryName } from '../../utils/translationUtils';


// Interfaces locales compatibles
interface CategoryLocal {
  _id?: string;
  id: string;
  name: string;
  image?: string;
  productCount?: number;
}

interface ProductLocal {
  id: string;
  _id?: string;
  name?: string;
  title?: string | Record<string, string>;
  price: number;
  image?: string;
  images?: string[];
  rating?: number;
  discount?: number;
}



interface CategoryWithProducts {
  category: CategoryLocal;
  products: ProductLocal[];
}

interface ProductSlidesProps {
  onProductPress?: (product: ProductLocal) => void;
  onCategoryPress?: (categoryId: string, categoryName: string) => void;
}

/**
 * Composant RelatedProduct - Section pour une catégorie avec ses produits
 * Reproduction du composant RelatedProduct du client web
 */
const RelatedProduct: React.FC<{
  title: string;
  products: ProductLocal[];
  categoryId: string;
  onProductPress?: (product: ProductLocal) => void;
  onSeeMorePress?: (categoryId: string, title: string) => void;
}> = ({ title, products, categoryId, onProductPress, onSeeMorePress }) => {
  
  if (!products || products.length === 0) {
    return null;
  }

  return (
    <View style={styles.relatedProductSection}>
      {/* Header de la section */}
      <View style={styles.sectionHeader}>
        <View style={styles.titleContainer}>
          <Text style={styles.sectionTitle}>{title}</Text>
          <View style={styles.titleUnderline} />
        </View>
        <TouchableOpacity 
          style={styles.seeMoreButton}
          onPress={() => onSeeMorePress?.(categoryId, title)}
        >
          <Text style={styles.seeMoreText}>See More</Text>
          <ELuxeIcon icon="arrowRight" size="sm" color={ELuxeColors.primary} />
        </TouchableOpacity>
      </View>

      {/* Liste des produits */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.productsContainer}
      >
        {products.map((product) => (
          <ProductCard
            key={product._id || product.id}
            product={product}
            onPress={() => onProductPress?.(product)}
            style={styles.productCard}
          />
        ))}
      </ScrollView>
    </View>
  );
};

/**
 * Composant ProductSlides principal
 * Reproduction de la logique ProductSlides.tsx du client web
 */
const ProductSlides: React.FC<ProductSlidesProps> = ({
  onProductPress,
  onCategoryPress,
}) => {
  const [categoriesWithProducts, setCategoriesWithProducts] = useState<CategoryWithProducts[]>([]);

  // Reproduction EXACTE de la logique du client web (components/home/<USER>
  useEffect(() => {
    const fetchData = async () => {
      try {
        // VERSION SIMPLIFIÉE: Récupérer les catégories showing (plus stable)
        const response = await ecommerceApi.getShowingCategories();
        const categories = response.data || response; // Extraction des données

        if (!categories || categories.length === 0) {
          setCategoriesWithProducts([]);
          return;
        }

        // ÉTAPE 2: Pour chaque catégorie, récupérer ses produits (EXACTEMENT comme ProductServices.getAllProducts)
        const categoriesWithProductsData = await Promise.all(
          categories.map(async (category: any) => {
            try {
              // Gestion du nom multilingue de la catégorie (comme le client web)

              // Appel EXACTEMENT comme le client web: ProductServices.getAllProducts
              const productResponse = await ecommerceApi.getProducts({
                category: category._id, // Utilise _id comme le client web
                limit: 18, // EXACTEMENT la même limite que le client web
                sortBy: 'created', // Équivalent à "date-updated-asc" du client web
                sortOrder: 'asc',
              });

              // Extraction des produits (comme le client web: { products })
              const products = productResponse.data?.products || productResponse.data || [];

              return {
                category,
                products: products || [], // Retourne les produits ou tableau vide
              };
            } catch (error) {
              return { category, products: [] }; // Retourne catégorie avec produits vides en cas d'erreur
            }
          }),
        );

        // ÉTAPE 3: Filtrer les catégories qui ont des produits (EXACTEMENT comme le client web)
        const validCategoriesWithProducts = categoriesWithProductsData.filter(
          (item: CategoryWithProducts) => item.products && item.products.length > 0
        );

        setCategoriesWithProducts(validCategoriesWithProducts);

      } catch (error) {
        setCategoriesWithProducts([]);
      }
    };

    fetchData();
  }, []);

  // Gestion du clic sur un produit
  const handleProductPress = (product: ProductLocal) => {
    onProductPress?.(product);
  };

  // Gestion du clic sur "See More"
  const handleSeeMorePress = (categoryId: string, categoryName: string) => {
    onCategoryPress?.(categoryId, categoryName);
  };

  // Si pas de données, ne rien afficher (comme le client web)
  if (categoriesWithProducts.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Reproduction exacte de la structure du client web */}
      {categoriesWithProducts.map((item: CategoryWithProducts) => (
        <RelatedProduct
          key={item.category._id || item.category.id}
          title={getCategoryName(item.category)}
          products={item.products}
          categoryId={item.category._id || item.category.id}
          onProductPress={handleProductPress}
          onSeeMorePress={handleSeeMorePress}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ComponentColors.screen.backgroundAlt,
  },
  relatedProductSection: {
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  titleContainer: {
    alignItems: 'flex-start',
  },
  sectionTitle: {
    color: ELuxeColors.textPrimary, // Noir pour maximum de contraste
    fontSize: 20, // Taille plus grande pour visibilité
    fontWeight: '700', // Très gras pour visibilité
    fontFamily: 'Roboto', // Police Android native
    marginBottom: 8,
    textAlign: 'left',
    lineHeight: 24, // Interligne pour lisibilité
  },
  titleUnderline: {
    width: 30,
    height: 2,
    backgroundColor: ELuxeColors.primary,
  },
  seeMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  seeMoreText: {
    ...TextStyles.buttonSmall,
    color: ELuxeColors.primary,
  },
  productsContainer: {
    paddingHorizontal: 16,
    gap: 12,
  },
  productCard: {
    width: 160,
    marginRight: 0, // Gap géré par contentContainerStyle
  },
});

export default ProductSlides;
