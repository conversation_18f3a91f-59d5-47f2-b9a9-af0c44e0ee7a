/**
 * Composant Services - Mobile E-Luxe 1.0
 * Reproduction EXACTE du composant Services.tsx du client web
 * Utilise storeCustomizationSetting.home.service_* depuis l'API
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
} from 'react-native';
import { useAppSelector } from '../store/store';
import { ELuxeColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import { getCategoryImage } from '../../utils/translationUtils';

// Interface pour un service (comme le client web)
interface ServiceItem {
  status: boolean;
  title: any; // Objet multilingue {fr, en}
  description: any; // Objet multilingue {fr, en}
  image: string;
}

/**
 * Composant ServiceItem - Reproduction exacte du tpservicesitem du client web
 */
interface ServiceItemProps {
  service: ServiceItem;
}

const ServiceItemComponent: React.FC<ServiceItemProps> = ({ service }) => {
  // Fonction pour extraire les valeurs multilingues (comme showingTranslateValue du client web)
  const getTranslatedValue = (value: any): string => {
    if (typeof value === 'string') return value;
    if (!value) return '';
    if (typeof value === 'object') {
      return value.en || value.fr || Object.values(value)[0] || '';
    }
    return String(value);
  };

  // Image par défaut (comme le client web)
  const getServiceImage = (imageUrl: string, fallback: string): string => {
    return (imageUrl && imageUrl.trim() !== '') 
      ? imageUrl 
      : `https://via.placeholder.com/60x60/D4AF37/FFFFFF?text=${fallback}`;
  };

  return (
    <View style={styles.serviceItem}>
      {/* Icône du service (comme tpservicesitem__icon) */}
      <View style={styles.serviceIcon}>
        <Image 
          source={{ 
            uri: getServiceImage(service.image, 'S')
          }} 
          style={styles.serviceImage} 
          resizeMode="contain"
          onError={() => {
            console.log('⚠️ Erreur chargement image service:', getTranslatedValue(service.title));
          }}
        />
      </View>
      
      {/* Contenu du service (comme tpservicesitem__content) */}
      <View style={styles.serviceContent}>
        <Text style={styles.serviceTitle}>
          {String(getTranslatedValue(service.title) || '')}
        </Text>
        <Text style={styles.serviceDescription}>
          {String(getTranslatedValue(service.description) || '')}
        </Text>
      </View>
    </View>
  );
};

/**
 * Composant Services principal - Reproduction exacte de Services.tsx du client web
 */
const Services: React.FC = () => {
  // Récupération des settings (EXACTEMENT comme le client web)
  const {storeCustomizationSetting, globalSetting} = useAppSelector(
    (state: any) => state.setting, // ← EXACTEMENT comme le client web (singulier)
  );

  // Essayer différentes sources pour les settings (robustesse)
  const homeSettings =
    storeCustomizationSetting?.home ||
    storeCustomizationSetting?.homeSettings ||
    globalSetting?.home;

  console.log('🔧 Services - storeCustomizationSetting:', storeCustomizationSetting);
  console.log('🔧 Services - globalSetting:', globalSetting);
  console.log('🔧 Services - homeSettings:', homeSettings);

  // Debug détaillé de la structure
  if (storeCustomizationSetting) {
    console.log('📋 Services - Clés de storeCustomizationSetting:', Object.keys(storeCustomizationSetting));
    if (storeCustomizationSetting.home) {
      console.log('🏠 Services - Clés de home:', Object.keys(storeCustomizationSetting.home));
      console.log('🔧 Services - service_one_status:', storeCustomizationSetting.home.service_one_status);
      console.log('🔧 Services - service_one_title:', storeCustomizationSetting.home.service_one_title);
    }
  }

  // Si pas de settings API, ne rien afficher - EXACTEMENT comme le client web
  if (!homeSettings) {
    console.log('⚠️ Services: Aucun homeSettings trouvé depuis l\'API, composant masqué');
    console.log(
      '📊 Services: Structure storeCustomizationSetting:',
      Object.keys(storeCustomizationSetting || {}),
    );

    // Ne rien afficher si pas de données API (comme le client web)
    return null;
  }

  // Construction des services (EXACTEMENT comme le client web)
  const services: ServiceItem[] = [
    {
      status: homeSettings.service_one_status,
      title: homeSettings.service_one_title,
      description: homeSettings.service_one_description,
      image: homeSettings.service_one_image,
    },
    {
      status: homeSettings.service_two_status,
      title: homeSettings.service_two_title,
      description: homeSettings.service_two_description,
      image: homeSettings.service_two_image,
    },
    {
      status: homeSettings.service_three_status,
      title: homeSettings.service_three_title,
      description: homeSettings.service_three_description,
      image: homeSettings.service_three_image,
    },
    {
      status: homeSettings.service_four_status,
      title: homeSettings.service_four_title,
      description: homeSettings.service_four_description,
      image: homeSettings.service_four_image,
    },
  ];

  // Filtrer les services actifs (comme le client web avec les conditions status)
  const activeServices = services.filter(service => service.status);

  // Si aucun service actif, ne rien afficher
  if (activeServices.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Section services (comme services-area pt-70) */}
      <View style={styles.servicesArea}>
        {/* Container (comme container) */}
        <View style={styles.servicesContainer}>
          {/* Row services (comme row services-gx-item) */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.servicesRow}
          >
            {activeServices.map((service, index) => (
              <View key={index} style={styles.serviceColumn}>
                <ServiceItemComponent service={service} />
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ELuxeColors.white,
  },
  servicesArea: {
    paddingTop: 20, // Équivalent pt-70 du client web (adapté mobile)
    paddingBottom: 20,
  },
  servicesContainer: {
    paddingHorizontal: 16,
  },
  servicesRow: {
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  serviceColumn: {
    width: 280, // Largeur fixe pour scroll horizontal
    marginRight: 16,
  },
  serviceItem: {
    flexDirection: 'row', // d-flex align-items-center du client web
    alignItems: 'center',
    backgroundColor: ELuxeColors.white, // Fond blanc pour meilleure visibilité
    borderRadius: 12,
    padding: 20, // Plus de padding pour uniformité
    marginBottom: 16, // mb-30 du client web
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1, // Ombre plus visible
    shadowRadius: 6,
    elevation: 4,
    borderWidth: 1,
    borderColor: ELuxeColors.border1, // Bordure pour définir les éléments
    height: 100, // Hauteur fixe uniforme
  },
  serviceIcon: {
    marginRight: 20, // mr-20 du client web
    width: 60, // Plus grand pour meilleure visibilité
    height: 60, // Plus grand pour meilleure visibilité
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primaryLight, // Fond coloré pour visibilité
    borderRadius: 30, // Cercle parfait
  },
  serviceImage: {
    width: 36, // Icône plus grande
    height: 36, // Icône plus grande
    tintColor: ELuxeColors.primary, // Couleur or pour les icônes
  },
  serviceContent: {
    flex: 1,
  },
  serviceTitle: {
    color: ELuxeColors.textPrimary, // Noir pour maximum de contraste
    fontSize: 18, // Taille plus grande pour visibilité
    fontWeight: '700', // Très gras pour visibilité
    fontFamily: 'Roboto', // Police Android native
    marginBottom: 4,
    lineHeight: 22, // Interligne pour lisibilité
  },
  serviceDescription: {
    ...TextStyles.bodySmall, // Équivalent p du client web
    color: ELuxeColors.textSecondary, // Gris foncé pour bon contraste
    lineHeight: 20, // Interligne plus grand pour meilleure lisibilité
    fontSize: 14, // Taille plus grande
  },
});

export default Services;
