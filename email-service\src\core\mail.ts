import config from "../config";
import nodemailer from "nodemailer";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-23
 *
 * Class NodeMailerManager
 */
class NodeMailerManager {
  public transporter: any;

  /**
   * Create a newDBManager instance.
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-03-22
   *
   */
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.mailHost,
      port: config.mailPort as number,
      secure: config.mailSecure,
      auth: config.mailAuth,
    });
  }

  /**
   * Send mail
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-23
   *
   * @param {any} data the email parametters
   *
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public sendMail({
    senderName = "E.LUXE LLC",
    senderEmail = "<EMAIL>",
    receivers = "",
    subject = "",
    body = "",
  } = {}): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        // send mail with defined transport object
        try {
          const info = await this.transporter.sendMail({
            from: `${senderName} <${senderEmail}>`, // sender address
            to: receivers, // list of receivers
            subject: subject, // Subject line
            // text: "Hello world?", // plain text body
            html: body, // html body
          });

          // console.log("Message sent: %s", info.messageId);
          // Message sent: <<EMAIL>>

          //
          // NOTE: You can go to https://forwardemail.net/my-account/emails to
          // see your email delivery status and preview
          // Or you can use the "preview-email" npm package to preview emails locally in browsers and iOS Simulator
          // <https://github.com/forwardemail/preview-email>
          //

          resolve(info);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }
}

const nodeMailerManager = new NodeMailerManager();
export default nodeMailerManager;
