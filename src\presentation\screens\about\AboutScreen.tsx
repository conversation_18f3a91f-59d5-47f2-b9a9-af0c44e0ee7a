/**
 * AboutScreen - Mobile E-Luxe 1.0
 * Page À propos reproduisant le contenu du client web
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ELuxeColors, ComponentColors } from '../../../theme/colors';
import { TextStyles } from '../../../theme/typography';
import Icon from 'react-native-vector-icons/MaterialIcons';

const AboutScreen: React.FC = () => {
  const navigation = useNavigation();

  const features = [
    {
      icon: 'diamond',
      title: 'Premium Quality',
      description: 'We source only the finest materials and work with trusted artisans to ensure exceptional quality in every product.',
    },
    {
      icon: 'local-shipping',
      title: 'Fast Delivery',
      description: 'Express shipping worldwide with tracking. Most orders arrive within 2-5 business days.',
    },
    {
      icon: 'security',
      title: 'Secure Shopping',
      description: 'Your privacy and security are our priority. All transactions are encrypted and protected.',
    },
    {
      icon: 'support-agent',
      title: '24/7 Support',
      description: 'Our dedicated customer service team is available around the clock to assist you.',
    },
  ];

  const stats = [
    { number: '50K+', label: 'Happy Customers' },
    { number: '10K+', label: 'Products Sold' },
    { number: '99%', label: 'Satisfaction Rate' },
    { number: '24/7', label: 'Customer Support' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Image
            source={{
              uri: 'https://via.placeholder.com/400x200/D4AF37/FFFFFF?text=E-Luxe+About',
            }}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroOverlay}>
            <Text style={styles.heroTitle}>About E-Luxe</Text>
            <Text style={styles.heroSubtitle}>
              Your gateway to luxury lifestyle
            </Text>
          </View>
        </View>

        {/* Story Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Our Story</Text>
          <Text style={styles.storyText}>
            Founded with a passion for excellence, E-Luxe has been curating the finest luxury products 
            for discerning customers worldwide. We believe that luxury is not just about price, but about 
            quality, craftsmanship, and the experience of owning something truly special.
          </Text>
          <Text style={styles.storyText}>
            Our journey began with a simple mission: to make luxury accessible to everyone who appreciates 
            the finer things in life. Today, we continue to uphold this vision by partnering with renowned 
            brands and emerging designers to bring you an exclusive collection of premium products.
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Why Choose E-Luxe</Text>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <View key={index} style={styles.featureCard}>
                <View style={styles.featureIcon}>
                  <Icon name={feature.icon} size={32} color={ELuxeColors.primary} />
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Stats Section */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Our Achievements</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <View key={index} style={styles.statCard}>
                <Text style={styles.statNumber}>{stat.number}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Mission Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Our Mission</Text>
          <View style={styles.missionCard}>
            <Text style={styles.missionText}>
              "To democratize luxury by providing access to premium products and exceptional service, 
              while maintaining the highest standards of quality and authenticity."
            </Text>
            <Text style={styles.missionAuthor}>- E-Luxe Team</Text>
          </View>
        </View>

        {/* Values Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Our Values</Text>
          <View style={styles.valuesList}>
            {[
              'Quality over quantity in everything we do',
              'Transparency in our business practices',
              'Sustainability and ethical sourcing',
              'Customer satisfaction as our top priority',
              'Innovation in luxury retail experience',
            ].map((value, index) => (
              <View key={index} style={styles.valueItem}>
                <Icon name="check-circle" size={20} color={ELuxeColors.primary} />
                <Text style={styles.valueText}>{value}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <Text style={styles.ctaTitle}>Ready to Experience Luxury?</Text>
          <Text style={styles.ctaDescription}>
            Discover our curated collection of premium products and join thousands of satisfied customers.
          </Text>
          <TouchableOpacity 
            style={styles.ctaButton}
            onPress={() => navigation.navigate('Shop')}
          >
            <Text style={styles.ctaButtonText}>Shop Now</Text>
            <Icon name="arrow-forward" size={20} color={ELuxeColors.white} />
          </TouchableOpacity>
        </View>

        {/* Contact CTA */}
        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>Have Questions?</Text>
          <Text style={styles.contactDescription}>
            Our team is here to help you find the perfect luxury products.
          </Text>
          <TouchableOpacity 
            style={styles.contactButton}
            onPress={() => navigation.navigate('Contact')}
          >
            <Text style={styles.contactButtonText}>Contact Us</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ComponentColors.screen.background,
  },
  heroSection: {
    position: 'relative',
    height: 250,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  heroTitle: {
    ...TextStyles.h1,
    color: ELuxeColors.white,
    textAlign: 'center',
    marginBottom: 8,
  },
  heroSubtitle: {
    ...TextStyles.h4,
    color: ELuxeColors.white,
    textAlign: 'center',
    opacity: 0.9,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionTitle: {
    ...TextStyles.h2,
    color: ELuxeColors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  storyText: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    lineHeight: 24,
    marginBottom: 16,
    textAlign: 'justify',
  },
  featuresGrid: {
    gap: 16,
  },
  featureCard: {
    backgroundColor: ELuxeColors.white,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featureIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: ELuxeColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    ...TextStyles.h4,
    color: ELuxeColors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  featureDescription: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  statsSection: {
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 20,
    paddingVertical: 32,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 20,
  },
  statNumber: {
    ...TextStyles.h1,
    color: ELuxeColors.white,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    ...TextStyles.body,
    color: ELuxeColors.white,
    textAlign: 'center',
    opacity: 0.9,
  },
  missionCard: {
    backgroundColor: ELuxeColors.white,
    padding: 24,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: ELuxeColors.primary,
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  missionText: {
    ...TextStyles.h4,
    color: ELuxeColors.textPrimary,
    fontStyle: 'italic',
    lineHeight: 24,
    marginBottom: 12,
  },
  missionAuthor: {
    ...TextStyles.body,
    color: ELuxeColors.primary,
    fontWeight: '600',
    textAlign: 'right',
  },
  valuesList: {
    gap: 12,
  },
  valueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  valueText: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    flex: 1,
  },
  ctaSection: {
    backgroundColor: ELuxeColors.white,
    paddingHorizontal: 20,
    paddingVertical: 32,
    alignItems: 'center',
  },
  ctaTitle: {
    ...TextStyles.h2,
    color: ELuxeColors.textPrimary,
    marginBottom: 12,
    textAlign: 'center',
  },
  ctaDescription: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  ctaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  ctaButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
    fontWeight: '600',
  },
  contactSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    alignItems: 'center',
  },
  contactTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  contactDescription: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
  },
  contactButton: {
    borderWidth: 2,
    borderColor: ELuxeColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  contactButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.primary,
    fontWeight: '600',
  },
});

export default AboutScreen;
