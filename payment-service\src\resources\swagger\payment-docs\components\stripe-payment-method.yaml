components:
  schemas:
    StripePaymentMethod:
      type: object
      properties:
        _id:
          type: string
        id:
          type: string
        object:
          type: string
        billing_details:
          type: object
          properties:
            address:
              type: object
              properties:
                city:
                  type: string
                country:
                  type: string
                line1:
                  type: string
                line2:
                  type: string
                postal_code:
                  type: string
                state:
                  type: string
        email:
          type: string
        name:
          type: string
        phone:
          type: string
        card:
          type: object
          properties:
            brand:
              type: string
            checks:
              type: object
              properties:
                address_line1_check:
                  type: string
                address_postal_code_check:
                  type: string
                cvc_check:
                  type: string
            country:
              type: string
            exp_month:
              type: number
            exp_year:
              type: number
            fingerprint:
              type: string
            funding:
              type: string
            generated_from:
              type: string
            last4:
              type: number
            networks:
              type: object
              properties:
                available:
                  type: array
                  items:
                preferred:
                  type: string
            three_d_secure_usage:
              type: object
              properties:
                supported:
                  type: boolean
            wallet:
              type: string

        balance:
          type: number
        created:
          type: number
        customer:
          type: string
        livemode:
          type: boolean
        metadata:
          type: object
          properties:
            order_id:
              type: string
        type:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time