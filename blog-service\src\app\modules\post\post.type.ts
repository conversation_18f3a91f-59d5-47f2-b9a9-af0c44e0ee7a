import { ObjectId } from "mongoose";
import TagType from "../tag/tag.type";

export default interface PostType {
  _id: string;
  slug: string;
  name: object;
  title: object;
  image: Array<string>;
  short_description: string;
  description: object;
  rating: number;
  num_reviews: number;
  reviews: Array<Review>;
  tags: Array<any>;
  tag: Array<any>;
  category: any;
  status: "show" | "hide";
  categories: Array<any>;
  related_posts: Array<PostType>;
  publish_date: Date;
  user_id: string;
  user_name: string;
}

interface Review {
  name: string;
  email: string;
  comment: string;
  rating: number;
}
