/**
 * Utilitaires de traduction - Mobile E-Luxe 1.0
 * Reproduction exacte de la logique du client web
 * Gère les objets multilingues {fr, en} de l'API
 * Intégré avec le système i18n
 */

import { getCurrentLanguage } from '../i18n/i18n';

/**
 * Extrait la valeur traduite d'un objet multilingue
 * Reproduction exacte de showingTranslateValue() du client web
 * 
 * @param value - Peut être une string ou un objet {fr, en, ...}
 * @param lang - Langue préférée (défaut: 'en')
 * @returns La valeur traduite ou la valeur originale
 */
export const showingTranslateValue = (value: any, lang?: string): string => {
  // Utilise la langue actuelle du système i18n si non spécifiée
  const targetLang = lang || getCurrentLanguage() || 'en';
  // Si c'est déjà une string, la retourner directement
  if (typeof value === 'string') {
    return value;
  }

  // Si c'est null ou undefined, retourner une string vide
  if (!value) {
    return '';
  }

  // Si c'est un objet avec des clés de langue
  if (typeof value === 'object' && value !== null) {
    // Essayer la langue demandée d'abord
    if (value[targetLang]) {
      return value[targetLang];
    }

    // Fallback vers l'anglais
    if (value['en']) {
      return value['en'];
    }

    // Fallback vers le français
    if (value['fr']) {
      return value['fr'];
    }

    // Fallback vers la première valeur disponible
    const keys = Object.keys(value);
    if (keys.length > 0) {
      return value[keys[0]];
    }
  }

  // Fallback final - convertir en string
  return String(value);
};

/**
 * Extrait le nom d'une catégorie (gère les objets multilingues)
 * Comme le client web
 */
export const getCategoryName = (category: any, lang: string = 'en'): string => {
  return showingTranslateValue(category?.name, lang);
};

/**
 * Extrait le titre d'un produit (gère les objets multilingues)
 * Comme le client web
 */
export const getProductTitle = (product: any, lang: string = 'en'): string => {
  return showingTranslateValue(product?.title || product?.name, lang);
};

/**
 * Extrait la description d'un produit (gère les objets multilingues)
 * Comme le client web
 */
export const getProductDescription = (product: any, lang: string = 'en'): string => {
  return showingTranslateValue(product?.description, lang);
};

/**
 * Formate le prix avec la devise
 * EXACTEMENT comme le client web
 */
export const formatPriceWithCurrency = (price: number | string, currency: string = 'USD'): string => {
  // Conversion en nombre si c'est une chaîne
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;

  if (!numPrice || isNaN(numPrice) || numPrice <= 0) {
    return '$0.00'; // Format par défaut comme le web
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(numPrice);
};

/**
 * Récupère le prix d'un produit EXACTEMENT comme le client web
 */
export const getProductPrice = (product: any): number => {
  // Priorité comme le client web: variants[0].price > prices.price > price > 0
  return product?.variants?.[0]?.price ||
         product?.prices?.price ||
         product?.price ||
         0;
};

/**
 * Récupère le prix original d'un produit EXACTEMENT comme le client web
 */
export const getProductOriginalPrice = (product: any): number => {
  // Priorité comme le client web: variants[0].original_price > prices.original_price > originalPrice
  return product?.variants?.[0]?.original_price ||
         product?.prices?.original_price ||
         product?.originalPrice ||
         getProductPrice(product);
};

/**
 * Vérifie si un produit est en promotion
 * Comme le client web
 */
export const isPromotional = (product: any): boolean => {
  return !!(product?.discount && product.discount > 0);
};

/**
 * Obtient la première image d'un produit
 * Comme le client web avec gestion d'erreur améliorée
 */
export const getProductImage = (product: any, defaultImage?: string): string => {
  const fallbackImage = defaultImage || 'https://via.placeholder.com/300x300/D4AF37/FFFFFF?text=E-Luxe';

  // Si c'est un tableau d'images
  if (Array.isArray(product?.image) && product.image.length > 0) {
    const imageUrl = product.image[0];
    return (imageUrl && imageUrl.trim() !== '') ? imageUrl : fallbackImage;
  }

  // Si c'est un tableau d'images dans images
  if (Array.isArray(product?.images) && product.images.length > 0) {
    const imageUrl = product.images[0];
    return (imageUrl && imageUrl.trim() !== '') ? imageUrl : fallbackImage;
  }

  // Si c'est une string directe
  if (typeof product?.image === 'string') {
    const imageUrl = product.image.trim();
    return imageUrl !== '' ? imageUrl : fallbackImage;
  }

  // Fallback vers l'image par défaut
  return fallbackImage;
};

/**
 * Obtient l'image d'une catégorie avec fallback
 * EXACTEMENT comme le client web (CategoryTable.tsx)
 */
export const getCategoryImage = (category: any, defaultImage?: string): string => {
  const fallbackImage = defaultImage || 'https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png';

  // Vérifier si l'image existe et n'est pas vide
  if (category?.image && typeof category.image === 'string' && category.image.trim() !== '') {
    return category.image.trim();
  }

  // Fallback vers l'image par défaut (comme le client web)
  return fallbackImage;
};
