import express, { Router } from "express";
import dotenv from "dotenv";
import routesGrouping from "../../utils/routes-grouping.util";
import postController from "./post.controller";

dotenv.config();

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-06-21
 *
 * Class PostRoutes
 */
class PostRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-06-21
   */
  constructor() {
    this.router = express.Router({ mergeParams: true });
  }

  /**
   * Creating all post routes
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-06-21
   *
   * @returns {Router} the post routes
   */
  public postRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        router.use(
          "/posts",
          routesGrouping.group((router) => {
            /**
             * @swagger
             * /v1/{lang}/blog/posts:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: filter
             *     summary: Filter posts.
             *     description: Filter posts by criteria.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: query
             *        name: page
             *        schema:
             *          type: number
             *          example: 1
             *        description: Pagination position, this
             *          position is set to 1 by default
             *      - in: query
             *        name: perPage
             *        schema:
             *          type: number
             *          example: 12
             *        description: The number of items per page, this
             *          number is set to 12 by default
             *      - in: query
             *        name: vendor
             *        schema:
             *          type: string
             *        description: The post's vendor
             *      - in: query
             *        name: name
             *        schema:
             *          type: string
             *        description: The post's name
             *      - in: query
             *        name: category
             *        schema:
             *          type: string
             *        description: The post's category name
             *      - in: query
             *        name: tag
             *        schema:
             *          type: string
             *        description: The post's tag name
             *      - in: query
             *        name: min
             *        schema:
             *          type: number
             *        description: Lowest post price
             *      - in: query
             *        name: max
             *        schema:
             *          type: number
             *        description: Highest post price
             *      - in: query
             *        name: rating
             *        schema:
             *          type: number
             *        description: Post rating
             *      - in: query
             *        name: order
             *        schema:
             *          type: string
             *          example: newest
             *        description: This value can be newest,
             *          lowest, highest, toprated
             *      - in: query
             *        name: featured
             *        schema:
             *          type: string
             *          example: 0
             *        description: Featured post
             *      - in: query
             *        name: promotional
             *        schema:
             *          type: string
             *          example: 0
             *        description: Promotional post
             *
             *     responses:
             *       200:
             *         description: Successfully retrieved posts.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    type: object
             *                    properties:
             *                      posts:
             *                        type: array
             *                        items:
             *                          $ref: '#/components/schemas/Post'
             *                      previousPage:
             *                        type: number
             *                        example: null
             *                      perPage:
             *                        type: number
             *                        example: 12
             *                      allProducts:
             *                        type: number
             *                        example: 12
             *                      currentPage:
             *                        type: number
             *                        example: 1
             *                      pages:
             *                        type: number
             *                        example: 1
             *                      nextPage:
             *                        type: number
             *                        example: 2
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get("/", postController.showPostsByFilter);

            /**
             * @swagger
             * /v1/{lang}/blog/posts/showing:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: showing
             *     summary: Get all posts shown.
             *     description: Get all posts shown.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *
             *     responses:
             *       200:
             *         description: Posts successfully retrieved.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    type: object
             *                    properties:
             *                      posts:
             *                        type: array
             *                        items:
             *                          $ref: '#/components/schemas/Post'
             *                      previousPage:
             *                        type: number
             *                        example: null
             *                      perPage:
             *                        type: number
             *                        example: 12
             *                      allProducts:
             *                        type: number
             *                        example: 12
             *                      currentPage:
             *                        type: number
             *                        example: 1
             *                      pages:
             *                        type: number
             *                        example: 1
             *                      nextPage:
             *                        type: number
             *                        example: 2
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get("/showing", postController.getShowingPosts);

            /**
             * @swagger
             * /v1/{lang}/blog/posts/showing/store:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: filter
             *     summary: Filter posts.
             *     description: Filter posts by criteria.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: query
             *        name: category
             *        schema:
             *          type: string
             *        description: The post category
             *      - in: query
             *        name: title
             *        schema:
             *          type: string
             *        description: The post title
             *      - in: query
             *        name: slug
             *        schema:
             *          type: string
             *        description: The post's slug
             *
             *     responses:
             *       200:
             *         description: Posts successfully retrieved.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    type: object
             *                    properties:
             *                      posts:
             *                        type: array
             *                        items:
             *                          $ref: '#/components/schemas/Post'
             *                      previousPage:
             *                        type: number
             *                        example: null
             *                      perPage:
             *                        type: number
             *                        example: 12
             *                      allPosts:
             *                        type: number
             *                        example: 12
             *                      currentPage:
             *                        type: number
             *                        example: 1
             *                      pages:
             *                        type: number
             *                        example: 1
             *                      nextPage:
             *                        type: number
             *                        example: 2
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get(
              "/showing/store",
              postController.getShowingStorePosts
            );

            /**
             * @swagger
             * /v1/{lang}/blog/posts:
             *   post:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: store
             *     summary: Add new post.
             *     description: Add new post.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *
             *     requestBody:
             *       required: true
             *       content:
             *         application/json:
             *           schema:
             *             type: object
             *             properties:
             *               sku:
             *                 type: string
             *                 description: The post's sku.
             *                 example: SKU0000245
             *               name:
             *                 type: string
             *                 description: The post's name.
             *                 example: Backpack
             *               vendor:
             *                 description: The vendor id
             *                 type: string
             *               image:
             *                 description: Post's image url
             *                 type: string
             *               brand:
             *                 description: Post's brand
             *                 type: string
             *               short_description:
             *                 description: Post's short description
             *                 type: string
             *               description:
             *                 description: Post's description
             *                 type: string
             *               price:
             *                 description: Post's price
             *                 type: number
             *                 example: 0.00
             *               promotional_price:
             *                 description: Post's promotional price
             *                 type: number
             *                 example: 0.00
             *               initialIn_stock:
             *                 description: Post's initial stock
             *                 type: number
             *                 example: 10
             *               current_stock:
             *                 description: Post's current stock
             *                 type: number
             *                 example: 10
             *               featured:
             *                 description: Featured post ? indicate 1
             *                  if and 0 otherwise
             *                 type: number
             *                 example: 0
             *               promotional:
             *                 description: Promotional post ? indicate 1
             *                  if and 0 otherwise
             *                 type: number
             *                 example: 0
             *               rating:
             *                 description: Post's rating.
             *                  Value between 1 and 5
             *                 type: number
             *                 example: 0
             *               numReviews:
             *                 description: Number of reviews
             *                 type: number
             *                 example: 0
             *               reviews:
             *                 type: array
             *                 items:
             *                   type: object
             *                   properties:
             *                     name:
             *                       type: string
             *                       description: Name of reviewer
             *                       example: Herman
             *                     email:
             *                       type: string
             *                       description: Email of reviewer
             *                       example: <EMAIL>
             *                     comment:
             *                       type: string
             *                       description: Comment of reviewer
             *                       example: An excellent post
             *                     rating:
             *                       type: number
             *                       description: Post's rating.
             *                          Value between 1 and 5
             *                       example: 1
             *               tags:
             *                 type: array
             *                 items:
             *                   type: string
             *                   description: Tag's id
             *               categories:
             *                 type: array
             *                 items:
             *                   type: string
             *                   description: Category's id
             *               related_posts:
             *                 type: array
             *                 items:
             *                   type: string
             *                   description: Related post's id
             *               store:
             *                 type: string
             *                 description: Store's id
             *               shipping:
             *                 type: object
             *                 properties:
             *                   weight:
             *                     type: number
             *                     description: the post weight
             *                   dimension:
             *                     type: object
             *                     properties:
             *                       length:
             *                         type: number
             *                         description: the post length
             *                       width:
             *                         type: number
             *                         description: the post width
             *                       height:
             *                         type: number
             *                         description: the post height
             *                   class:
             *                     type: string
             *                     description: Shipping class
             *             required:
             *               - sku
             *               - name
             *               - price
             *
             *     responses:
             *       201:
             *         description: Post successfully created.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.post("/", postController.store);

            /**
             * @swagger
             * /v1/{lang}/blog/posts/many:
             *   post:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: storeMany
             *     summary: Add multiple posts.
             *     description: Add multiple posts.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *
             *     requestBody:
             *       required: true
             *       content:
             *         application/json:
             *           schema:
             *             type: array
             *             items:
             *               type: object
             *               properties:
             *                 sku:
             *                   type: string
             *                   description: The post's sku.
             *                   example: SKU0000245
             *                 name:
             *                   type: string
             *                   description: The post's name.
             *                   example: Backpack
             *                 vendor:
             *                   description: The vendor id
             *                   type: string
             *                 image:
             *                   description: Post's image url
             *                   type: string
             *                 brand:
             *                   description: Post's brand
             *                   type: string
             *                 short_description:
             *                   description: Post's short description
             *                   type: string
             *                 description:
             *                   description: Post's description
             *                   type: string
             *                 price:
             *                   description: Post's price
             *                   type: number
             *                   example: 0.00
             *                 promotional_price:
             *                   description: Post's promotional price
             *                   type: number
             *                   example: 0.00
             *                 initialIn_stock:
             *                   description: Post's initial stock
             *                   type: number
             *                   example: 10
             *                 current_stock:
             *                   description: Post's current stock
             *                   type: number
             *                   example: 10
             *                 featured:
             *                   description: Featured post ? indicate 1
             *                    if and 0 otherwise
             *                   type: number
             *                   example: 0
             *                 promotional:
             *                   description: Promotional post ? indicate 1
             *                    if and 0 otherwise
             *                   type: number
             *                   example: 0
             *                 rating:
             *                   description: Post's rating.
             *                    Value between 1 and 5
             *                   type: number
             *                   example: 0
             *                 numReviews:
             *                   description: Number of reviews
             *                   type: number
             *                   example: 0
             *                 reviews:
             *                   type: array
             *                   items:
             *                     type: object
             *                     properties:
             *                       name:
             *                         type: string
             *                         description: Name of reviewer
             *                         example: Herman
             *                       email:
             *                         type: string
             *                         description: Email of reviewer
             *                         example: <EMAIL>
             *                       comment:
             *                         type: string
             *                         description: Comment of reviewer
             *                         example: An excellent post
             *                       rating:
             *                         type: number
             *                         description: Post's rating.
             *                          Value between 1 and 5
             *                         example: 1
             *                 tags:
             *                   type: array
             *                   items:
             *                     type: string
             *                     description: Tag's id
             *                 categories:
             *                   type: array
             *                   items:
             *                     type: string
             *                     description: Category's id
             *                 related_posts:
             *                   type: array
             *                   items:
             *                     type: string
             *                     description: Related post's id
             *                 store:
             *                   type: string
             *                   description: Store's id
             *                 shipping:
             *                   type: object
             *                   properties:
             *                     weight:
             *                       type: number
             *                       description: the post weight
             *                     dimension:
             *                       type: object
             *                       properties:
             *                         length:
             *                           type: number
             *                           description: the post length
             *                         width:
             *                           type: number
             *                           description: the post width
             *                         height:
             *                           type: number
             *                           description: the post height
             *                     class:
             *                       type: string
             *                       description: Shipping class
             *             required:
             *               - sku
             *               - name
             *               - price
             *
             *     responses:
             *       201:
             *         description: Posts successfully created.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.post("/many", postController.storeMultiple);

            /**
             * @swagger
             * /v1/{lang}/blog/posts/many:
             *   put:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: updateMany
             *     summary: Update multiple posts.
             *     description: Update multiple posts.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *
             *     requestBody:
             *       required: true
             *       content:
             *         application/json:
             *           schema:
             *             type: array
             *             items:
             *               type: object
             *               properties:
             *                 sku:
             *                   type: string
             *                   description: The post's sku.
             *                   example: SKU0000245
             *                 name:
             *                   type: string
             *                   description: The post's name.
             *                   example: Backpack
             *                 vendor:
             *                   description: The vendor id
             *                   type: string
             *                 image:
             *                   description: Post's image url
             *                   type: string
             *                 brand:
             *                   description: Post's brand
             *                   type: string
             *                 short_description:
             *                   description: Post's short description
             *                   type: string
             *                 description:
             *                   description: Post's description
             *                   type: string
             *                 price:
             *                   description: Post's price
             *                   type: number
             *                   example: 0.00
             *                 promotional_price:
             *                   description: Post's promotional price
             *                   type: number
             *                   example: 0.00
             *                 initialIn_stock:
             *                   description: Post's initial stock
             *                   type: number
             *                   example: 10
             *                 current_stock:
             *                   description: Post's current stock
             *                   type: number
             *                   example: 10
             *                 featured:
             *                   description: Featured post ? indicate 1 if and 0 otherwise
             *                   type: number
             *                   example: 0
             *                 promotional:
             *                   description: Promotional post ? indicate 1 if and 0 otherwise
             *                   type: number
             *                   example: 0
             *                 rating:
             *                   description: Post's rating. Value between 1 and 5
             *                   type: number
             *                   example: 0
             *                 numReviews:
             *                   description: Number of reviews
             *                   type: number
             *                   example: 0
             *                 reviews:
             *                   type: array
             *                   items:
             *                     type: object
             *                     properties:
             *                       name:
             *                         type: string
             *                         description: Name of reviewer
             *                         example: Herman
             *                       email:
             *                         type: string
             *                         description: Email of reviewer
             *                         example: <EMAIL>
             *                       comment:
             *                         type: string
             *                         description: Comment of reviewer
             *                         example: An excellent post
             *                       rating:
             *                         type: number
             *                         description: Post's rating. Value between 1 and 5
             *                         example: 1
             *                 tags:
             *                   type: array
             *                   items:
             *                     type: string
             *                     description: Tag's id
             *                 categories:
             *                   type: array
             *                   items:
             *                     type: string
             *                     description: Category's id
             *                 related_posts:
             *                   type: array
             *                   items:
             *                     type: string
             *                     description: Related post's id
             *                 store:
             *                   type: string
             *                   description: Store's id
             *                 shipping:
             *                   type: object
             *                   properties:
             *                     weight:
             *                       type: number
             *                       description: the post weight
             *                     dimension:
             *                       type: object
             *                       properties:
             *                         length:
             *                           type: number
             *                           description: the post length
             *                         width:
             *                           type: number
             *                           description: the post width
             *                         height:
             *                           type: number
             *                           description: the post height
             *                     class:
             *                       type: string
             *                       description: Shipping class
             *             required:
             *               - sku
             *               - name
             *               - price
             *
             *     responses:
             *       200:
             *         description: Posts successfully created.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.put("/many", postController.updateMany);

            /**
             * @swagger
             * /v1/{lang}/blog/posts/{postIds}/many:
             *   delete:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: deleteMany
             *     summary: Delete many posts.
             *     description: Delete many posts.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postIds
             *        schema:
             *          type: string
             *        required: true
             *        description: The post IDs to be deleted. You can enter several identifiers,
             *          separated by commas
             *
             *     responses:
             *       204:
             *         description: Posts deleted successfully.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.delete("/:postIds/many", postController.deleteMany);
          })
        );

        router.use(
          "/post",
          routesGrouping.group((router) => {
            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: byId
             *     summary: Get a post by id.
             *     description: Get a post by id from the system.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: Post's id
             *
             *     responses:
             *       200:
             *         description: The post has been successfully obtained.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get("/:postId", postController.showPostById);

            /**
             * @swagger
             * /v1/{lang}/blog/post/slug/{slug}:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: bySlug
             *     summary: Get post by slug.
             *     description: Get a post from the system by name (slug).
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: slug
             *        schema:
             *          type: string
             *        required: true
             *        description: Post's id
             *
             *     responses:
             *       200:
             *         description: The post has been successfully obtained.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get("/slug/:slug", postController.getPostBySlug);

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}/category/{categoryId}/assign:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: assignCategory
             *     summary: Assign a category to a post.
             *     description: Assign a category to a post.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to get
             *      - in: path
             *        name: categoryId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the category to get
             *
             *     responses:
             *       200:
             *         description: The category successfully assigned to a
             *                      post.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get(
              "/:postId/category/:categoryId/assign",
              postController.assignToCategory
            );

            router.post("/:postId/reviews", postController.createReview);

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}/category/{categoryId}/unassign:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: unassignCategory
             *     summary: Unassign a category to a post.
             *     description: Unassign a category to a post.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to get
             *      - in: path
             *        name: categoryId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the category to get
             *
             *     responses:
             *       200:
             *         description: The category successfully unassigned to a
             *                      post.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get(
              "/:postId/category/:categoryId/unassign",
              postController.unassignFromCategory
            );

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}/tag/{tagId}/assign:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: assignTag
             *     summary: Assign a tag to a post.
             *     description: Assign a tag to a post.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to get
             *      - in: path
             *        name: tagId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the tag to get
             *
             *     responses:
             *       200:
             *         description: The tag successfully assigned to a
             *                      post.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get(
              "/:postId/tag/:tagId/assign",
              postController.assignToTag
            );

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}/tag/{tagId}/unassign:
             *   get:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: unassignCategory
             *     summary: Unassign a tag to a post.
             *     description: Unassign a tag to a post.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to get
             *      - in: path
             *        name: tagId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the tag to get
             *
             *     responses:
             *       200:
             *         description: The tag successfully unassigned to a
             *                      post.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.get(
              "/:postId/tag/:tagId/unassign",
              postController.unassignFromTag
            );

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}:
             *   put:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: update
             *     summary: Update a post.
             *     description: Update a post.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to get
             *
             *     requestBody:
             *       required: true
             *       content:
             *         application/json:
             *           schema:
             *             type: object
             *             properties:
             *               sku:
             *                 type: string
             *                 description: The post's sku.
             *                 example: SKU0000245
             *               name:
             *                 type: string
             *                 description: The post's name.
             *                 example: Backpack
             *               vendor:
             *                 description: The vendor id
             *                 type: string
             *               image:
             *                 description: Post's image url
             *                 type: string
             *               brand:
             *                 description: Post's brand
             *                 type: string
             *               short_description:
             *                 description: Post's short description
             *                 type: string
             *               description:
             *                 description: Post's description
             *                 type: string
             *               price:
             *                 description: Post's price
             *                 type: number
             *                 example: 0.00
             *               promotional_price:
             *                 description: Post's promotional price
             *                 type: number
             *                 example: 0.00
             *               initialIn_stock:
             *                 description: Post's initial stock
             *                 type: number
             *                 example: 10
             *               current_stock:
             *                 description: Post's current stock
             *                 type: number
             *                 example: 10
             *               featured:
             *                 description: Featured post ? indicate 1
             *                  if and 0 otherwise
             *                 type: number
             *                 example: 0
             *               promotional:
             *                 description: Promotional post ? indicate 1
             *                  if and 0 otherwise
             *                 type: number
             *                 example: 0
             *               rating:
             *                 description: Post's rating.
             *                  Value between 1 and 5
             *                 type: number
             *                 example: 0
             *               numReviews:
             *                 description: Number of reviews
             *                 type: number
             *                 example: 0
             *               reviews:
             *                 type: array
             *                 items:
             *                   type: object
             *                   properties:
             *                     name:
             *                       type: string
             *                       description: Name of reviewer
             *                       example: Herman
             *                     email:
             *                       type: string
             *                       description: Email of reviewer
             *                       example: <EMAIL>
             *                     comment:
             *                       type: string
             *                       description: Comment of reviewer
             *                       example: An excellent post
             *                     rating:
             *                       type: number
             *                       description: Post's rating.
             *                          Value between 1 and 5
             *                       example: 1
             *               tags:
             *                 type: array
             *                 items:
             *                   type: string
             *                   description: Tag's id
             *               categories:
             *                 type: array
             *                 items:
             *                   type: string
             *                   description: Category's id
             *               related_posts:
             *                 type: array
             *                 items:
             *                   type: string
             *                   description: Related post's id
             *               store:
             *                 type: string
             *                 description: Store's id
             *               shipping:
             *                 type: object
             *                 properties:
             *                   weight:
             *                     type: number
             *                     description: the post weight
             *                   dimension:
             *                     type: object
             *                     properties:
             *                       length:
             *                         type: number
             *                         description: the post length
             *                       width:
             *                         type: number
             *                         description: the post width
             *                       height:
             *                         type: number
             *                         description: the post height
             *                   class:
             *                     type: string
             *                     description: Shipping class
             *             required:
             *               - sku
             *               - name
             *               - price
             *
             *     responses:
             *       202:
             *         description: Post successfully updated.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       401:
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.put("/:postId", postController.update);

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}:
             *   patch:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: patch
             *     summary: Patch a post by ID.
             *     description: Patch a post by ID.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to get
             *
             *     requestBody:
             *       required: true
             *       description: We can find the documentation on the JSON
             *                    Patch format [here](https://jsonpatch.com/)
             *       content:
             *         application/json:
             *           schema:
             *             $ref: '#/components/schemas/PatchBody'
             *
             *     responses:
             *       200:
             *         description: The post has successfully patched.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Post'
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.patch("/:postId", postController.patch);

            /**
             * @swagger
             * /v1/{lang}/blog/post/{postId}:
             *   delete:
             *     security:
             *      - bearerAuth: []
             *     tags:
             *     - Post
             *     operationId: delete
             *     summary: Delete a post by ID.
             *     description: Delete a post by ID.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *      - in: path
             *        name: postId
             *        schema:
             *          type: string
             *        required: true
             *        description: String ID of the post to delete
             *
             *     responses:
             *       204:
             *         description: The post deleted successfully.
             *
             *       '400':
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       '401':
             *         description: Unauthorized.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/401'
             *
             *       '404':
             *         description: Not Found.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/404'
             *
             *       '500':
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.delete("/:postId", postController.delete);
          })
        );
      })
    );
  }
}

const postRoutes = new PostRoutes();
export default postRoutes;
