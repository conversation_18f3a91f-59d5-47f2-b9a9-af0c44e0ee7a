/**
 * Composant HeaderWithMenu - Mobile E-Luxe 1.0
 * Reproduction exacte du HeaderSticky.tsx du client web
 * Avec logo, menu principal, recherche, et actions
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { useAppSelector } from '../store/store';
import { selectLogo, selectGlobalSetting } from '../store/slices/settingsSlice';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ELuxeColors, ComponentColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import LanguageSwitcher from './LanguageSwitcher';

const { width } = Dimensions.get('window');

// Menu principal (comme MainMenu.json du client web)
const MAIN_MENU = [
  { id: 'home', title: 'Home', url: 'Home', i18n: 'home' },
  { id: 'shop', title: 'Shop', url: 'Shop', i18n: 'shop' },
  { id: 'categories', title: 'Categories', url: 'Categories', i18n: 'categories' },
  { id: 'products', title: 'Products', url: 'ProductList', i18n: 'products' },
  { id: 'about', title: 'About', url: 'About', i18n: 'about' },
  { id: 'contact', title: 'Contact', url: 'Contact', i18n: 'contact' },
];

interface HeaderWithMenuProps {
  onMenuPress?: (menuItem: any) => void;
  onCartPress?: () => void;
  onSearchPress?: () => void;
  onAccountPress?: () => void;
  cartItemCount?: number;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  onSearchSubmit?: (query: string) => void;
}

/**
 * Composant HeaderWithMenu principal
 * Reproduction de la structure HeaderSticky du client web
 */
const HeaderWithMenu: React.FC<HeaderWithMenuProps> = ({
  onMenuPress,
  onCartPress,
  onSearchPress,
  onAccountPress,
  cartItemCount = 0,
  searchQuery = '',
  onSearchChange,
  onSearchSubmit,
}) => {
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  
  // Sélecteurs Redux (comme le client web)
  const logo = useAppSelector(selectLogo);
  const globalSetting = useAppSelector(selectGlobalSetting);

  // Gestion du menu mobile
  const handleMenuToggle = () => {
    setIsMenuVisible(!isMenuVisible);
  };

  const handleMenuItemPress = (menuItem: any) => {
    setIsMenuVisible(false);
    onMenuPress?.(menuItem);
  };

  // Gestion de la recherche
  const handleSearchToggle = () => {
    setIsSearchVisible(!isSearchVisible);
  };

  const handleSearchSubmit = () => {
    onSearchSubmit?.(searchQuery);
    setIsSearchVisible(false);
  };

  return (
    <>
      {/* Header principal (comme HeaderSticky) */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          
          {/* Section Logo (col-xl-2 col-lg-3) */}
          <View style={styles.logoSection}>
            <TouchableOpacity style={styles.logoContainer}>
              {logo ? (
                <Image source={{ uri: logo }} style={styles.logo} resizeMode="contain" />
              ) : (
                <Text style={styles.logoText}>{globalSetting?.shop_name || 'E-Luxe'}</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Section Menu (col-xl-6 col-lg-6) - Caché sur mobile, bouton hamburger */}
          <View style={styles.menuSection}>
            <TouchableOpacity style={styles.menuButton} onPress={handleMenuToggle}>
              <Icon name="menu" size={24} color={ELuxeColors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Section Actions (col-xl-4 col-lg-3) */}
          <View style={styles.actionsSection}>

            {/* Sélecteur de langue */}
            <LanguageSwitcher compact={true} showLabel={false} />

            {/* Recherche */}
            <TouchableOpacity style={styles.actionButton} onPress={handleSearchToggle}>
              <ELuxeIcon icon="search" size="md" color={ELuxeColors.textPrimary} />
            </TouchableOpacity>

            {/* Compte utilisateur */}
            <TouchableOpacity style={styles.actionButton} onPress={onAccountPress}>
              <ELuxeIcon icon="account" size="md" color={ELuxeColors.textPrimary} />
            </TouchableOpacity>

            {/* Panier avec badge */}
            <TouchableOpacity style={styles.actionButton} onPress={onCartPress}>
              <ELuxeIcon icon="cart" size="md" color={ELuxeColors.textPrimary} />
              {cartItemCount > 0 && (
                <View style={styles.cartBadge}>
                  <Text style={styles.cartBadgeText}>{cartItemCount}</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Barre de recherche étendue (si visible) */}
        {isSearchVisible && (
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <ELuxeIcon icon="search" size="md" color={ELuxeColors.textSecondary} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search products..."
                placeholderTextColor={ELuxeColors.textTertiary}
                value={searchQuery}
                onChangeText={onSearchChange}
                onSubmitEditing={handleSearchSubmit}
                returnKeyType="search"
                autoFocus
              />
              <TouchableOpacity onPress={() => setIsSearchVisible(false)}>
                <ELuxeIcon icon="close" size="md" color={ELuxeColors.textSecondary} />
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Menu Modal (comme le menu mobile du client web) */}
      <Modal
        visible={isMenuVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsMenuVisible(false)}
      >
        <View style={styles.menuOverlay}>
          <View style={styles.menuContainer}>
            
            {/* Header du menu */}
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Menu</Text>
              <TouchableOpacity onPress={() => setIsMenuVisible(false)}>
                <ELuxeIcon icon="close" size="lg" color={ELuxeColors.textPrimary} />
              </TouchableOpacity>
            </View>

            {/* Items du menu */}
            <ScrollView style={styles.menuContent}>
              {MAIN_MENU.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.menuItem}
                  onPress={() => handleMenuItemPress(item)}
                >
                  <Text style={styles.menuItemText}>{item.title}</Text>
                  <ELuxeIcon icon="arrowRight" size="sm" color={ELuxeColors.textSecondary} />
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Footer du menu */}
            <View style={styles.menuFooter}>
              <Text style={styles.menuFooterText}>
                {globalSetting?.shop_name || 'E-Luxe'} - Luxury Shopping
              </Text>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: ComponentColors.header.background,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
    shadowColor: ComponentColors.header.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8, // Augmenté pour plus de visibilité
    position: 'absolute', // Sticky header comme le web
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000, // Au-dessus des autres éléments
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 70, // Augmenté de 60 à 70 pour accommoder le logo plus grand
  },
  logoSection: {
    flex: 1,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 180, // Encore plus grand pour mobile (comme le web mobile)
    height: 65,  // Proportionnel pour meilleure visibilité
    // Taille optimisée pour la visibilité mobile
  },
  logoText: {
    ...TextStyles.brand,
    color: ELuxeColors.primary,
  },
  menuSection: {
    alignItems: 'center',
  },
  menuButton: {
    padding: 8,
  },
  actionsSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    padding: 8,
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: ELuxeColors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    ...TextStyles.caption,
    color: ELuxeColors.white,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.grey2,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    ...TextStyles.input,
    color: ELuxeColors.textPrimary,
  },
  menuOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  menuContainer: {
    backgroundColor: ELuxeColors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
  },
  menuTitle: {
    ...TextStyles.h4,
    color: ELuxeColors.textPrimary,
  },
  menuContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border2,
  },
  menuItemText: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
  },
  menuFooter: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: ELuxeColors.border1,
    alignItems: 'center',
  },
  menuFooterText: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
  },
});

export default HeaderWithMenu;
