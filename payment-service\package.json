{"name": "payment-server", "version": "1.0.0", "description": "This is a service to manage Users API", "main": "server.ts", "//type": "module", "scripts": {"start": "nodemon --watch src/** --ext ts,json --ignore src/**/*.spec.ts --exec ts-node --files server.ts", "start-dev": "NODE_ENV=development nodemon --watch src/** --ext ts,json --ignore src/**/*.spec.ts --exec ts-node --files server.ts", "start-prod": "NODE_ENV=production nodemon --watch src/** --ext ts,json --ignore src/**/*.spec.ts --exec ts-node --files server.ts", "lint": "eslint .", "test": "jest", "build": "tsc --build --force tsconfig.json && cp -r src/resources build/src/"}, "author": "Iosys Pvt Ltd", "license": "MIT", "devDependencies": {"@types/amqplib": "^0.10.1", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/i18n": "^0.13.6", "@types/jest": "^29.5.1", "@types/node": "^18.16.16", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/validatorjs": "^3.15.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8"}, "dependencies": {"@paypal/paypal-server-sdk": "^0.6.1", "amqplib": "^0.10.3", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.1.0", "eslint": "^8.41.0", "express": "^4.5.0", "express-async-handler": "^1.2.0", "express-jwt": "^8.4.1", "i18n": "^0.15.1", "jest": "^29.5.0", "mongoose": "^7.2.2", "nodemon": "^2.0.22", "redis": "^4.6.6", "stripe": "^14.10.0", "supertest": "^6.3.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "tslib": "^2.8.1", "typescript": "^5.0.4", "validatorjs": "^3.22.1"}}