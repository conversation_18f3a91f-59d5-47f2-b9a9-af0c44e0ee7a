/**
 * EcommerceApiService - Service API unifié pour E-Luxe Mobile
 * Centralise tous les appels API pour l'e-commerce
 */

import { ApiService } from './ApiService';
import ProductServices from './product/ProductServices';
import CategoryServices from './product/CategoryServices';
import OrderServices from './order/OrderServices';
import SettingServices from './setting/SettingServices';

export class EcommerceApiService {
  private apiService: ApiService;
  private productServices: ProductServices;
  private categoryServices: CategoryServices;
  private orderServices: OrderServices;
  private settingServices: SettingServices;

  constructor() {
    this.apiService = new ApiService();
    this.productServices = ProductServices;
    this.categoryServices = CategoryServices;
    this.orderServices = OrderServices;
    this.settingServices = SettingServices;
  }

  // ===== PRODUITS =====

  /**
   * Récupère la liste des produits avec pagination et filtres
   */
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    try {
      const response = await this.productServices.getAllProducts(params || {});
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère un produit par son ID
   */
  async getProductById(productId: string) {
    try {
      const response = await this.productServices.getProductById(productId);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les produits en vedette (showing products)
   */
  async getFeaturedProducts() {
    try {
      const response = await this.productServices.getShowingProducts({});
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les meilleurs vendeurs (utilise showing products pour l'instant)
   */
  async getBestSellersOfMonth() {
    try {
      const response = await this.productServices.getShowingProducts({});
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== CATÉGORIES =====

  /**
   * Récupère toutes les catégories
   */
  async getCategories() {
    try {
      const response = await this.categoryServices.getAllCategories();
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les top catégories (utilise getAllCategory pour l'instant)
   */
  async getTopCategories() {
    try {
      const response = await this.categoryServices.getAllCategory();
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== COMMANDES =====

  /**
   * Crée une nouvelle commande (pour l'instant, retourne un mock)
   */
  async createOrder(orderData: any) {
    try {
      // TODO: Implémenter la vraie création de commande
      const response = {
        id: Date.now().toString(),
        order_number: `ORD-${Date.now()}`,
        ...orderData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les commandes de l'utilisateur
   */
  async getOrders(page: number = 1, limit: number = 10) {
    try {
      const response = await this.orderServices.getAllOrders({ page, limit });
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les détails d'une commande
   */
  async getOrderById(orderId: string) {
    try {
      const response = await this.orderServices.getOrderById(orderId);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== PARAMÈTRES =====

  /**
   * Récupère les paramètres globaux
   */
  async getGlobalSettings() {
    try {
      const response = await this.settingServices.getGlobalSetting();
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les paramètres de personnalisation du magasin
   */
  async getStoreCustomizationSettings() {
    try {
      const response = await this.settingServices.getStoreCustomizationSetting();
      return response;
    } catch (error) {
      throw error;
    }
  }
}

// Instance singleton
export const ecommerceService = new EcommerceApiService();
