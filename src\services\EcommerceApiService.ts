/**
 * EcommerceApiService - Service API unifié pour E-Luxe Mobile
 * Centralise tous les appels API pour l'e-commerce
 */

import { ApiService } from './ApiService';
import ProductServices from './product/ProductServices';
import CategoryServices from './product/CategoryServices';
import OrderServices from './order/OrderServices';
import SettingServices from './setting/SettingServices';

export class EcommerceApiService {
  private apiService: ApiService;
  private productServices: ProductServices;
  private categoryServices: CategoryServices;
  private orderServices: OrderServices;
  private settingServices: SettingServices;

  constructor() {
    this.apiService = new ApiService();
    this.productServices = ProductServices;
    this.categoryServices = CategoryServices;
    this.orderServices = OrderServices;
    this.settingServices = SettingServices;
  }

  // ===== PRODUITS =====

  /**
   * Récupère la liste des produits avec pagination et filtres
   */
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    console.log('📦 EcommerceApiService.getProducts - params:', params);

    try {
      const response = await this.productServices.getAllProducts(params || {});
      console.log('✅ Produits récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération produits:', error);
      throw error;
    }
  }

  /**
   * Récupère un produit par son ID
   */
  async getProductById(productId: string) {
    console.log('🔍 EcommerceApiService.getProductById - ID:', productId);

    try {
      const response = await this.productServices.getProductById(productId);
      console.log('✅ Produit récupéré:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération produit:', error);
      throw error;
    }
  }

  /**
   * Récupère les produits en vedette (showing products)
   */
  async getFeaturedProducts() {
    console.log('⭐ EcommerceApiService.getFeaturedProducts');

    try {
      const response = await this.productServices.getShowingProducts({});
      console.log('✅ Produits en vedette récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération produits en vedette:', error);
      throw error;
    }
  }

  /**
   * Récupère les meilleurs vendeurs (utilise showing products pour l'instant)
   */
  async getBestSellersOfMonth() {
    console.log('🏆 EcommerceApiService.getBestSellersOfMonth');

    try {
      const response = await this.productServices.getShowingProducts({});
      console.log('✅ Meilleurs vendeurs récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération meilleurs vendeurs:', error);
      throw error;
    }
  }

  // ===== CATÉGORIES =====

  /**
   * Récupère toutes les catégories
   */
  async getCategories() {
    console.log('📂 EcommerceApiService.getCategories');

    try {
      const response = await this.categoryServices.getAllCategories();
      console.log('✅ Catégories récupérées:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération catégories:', error);
      throw error;
    }
  }

  /**
   * Récupère les top catégories (utilise getAllCategory pour l'instant)
   */
  async getTopCategories() {
    console.log('🔝 EcommerceApiService.getTopCategories');

    try {
      const response = await this.categoryServices.getAllCategory();
      console.log('✅ Top catégories récupérées:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération top catégories:', error);
      throw error;
    }
  }

  // ===== COMMANDES =====

  /**
   * Crée une nouvelle commande (pour l'instant, retourne un mock)
   */
  async createOrder(orderData: any) {
    console.log('🛒 EcommerceApiService.createOrder - data:', orderData);

    try {
      // TODO: Implémenter la vraie création de commande
      const response = {
        id: Date.now().toString(),
        order_number: `ORD-${Date.now()}`,
        ...orderData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      console.log('✅ Commande créée (mock):', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur création commande:', error);
      throw error;
    }
  }

  /**
   * Récupère les commandes de l'utilisateur
   */
  async getOrders(page: number = 1, limit: number = 10) {
    console.log('📋 EcommerceApiService.getOrders - page:', page);

    try {
      const response = await this.orderServices.getAllOrders({ page, limit });
      console.log('✅ Commandes récupérées:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération commandes:', error);
      throw error;
    }
  }

  /**
   * Récupère les détails d'une commande
   */
  async getOrderById(orderId: string) {
    console.log('🔍 EcommerceApiService.getOrderById - ID:', orderId);
    
    try {
      const response = await this.orderServices.getOrderById(orderId);
      console.log('✅ Détails commande récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération détails commande:', error);
      throw error;
    }
  }

  // ===== PARAMÈTRES =====

  /**
   * Récupère les paramètres globaux
   */
  async getGlobalSettings() {
    console.log('⚙️ EcommerceApiService.getGlobalSettings');

    try {
      const response = await this.settingServices.getGlobalSetting();
      console.log('✅ Paramètres globaux récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération paramètres globaux:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de personnalisation du magasin
   */
  async getStoreCustomizationSettings() {
    console.log('🎨 EcommerceApiService.getStoreCustomizationSettings');

    try {
      const response = await this.settingServices.getStoreCustomizationSetting();
      console.log('✅ Paramètres de personnalisation récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur récupération paramètres personnalisation:', error);
      throw error;
    }
  }
}

// Instance singleton
export const ecommerceService = new EcommerceApiService();
