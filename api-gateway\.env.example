# Server config
NODE_SERVER_PORT=2000
NODE_SERVER_HOST=localhost
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
NODE_SERVER_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApyKQ4/xUF3U8OUP6QKES\nMHQxSeUmxlAasCdLQBHU6W8X4Uyg7CvMxzkW60mBbtXVDcnunLYQkVSXerfDgvwp\nc9QgRly/4DfHBmM51TCL0EPOHYubiSg35ouf3+N2B/0T0vIz5NY4Bo+Rs93RKPfR\nvq1FyUpIVWNy3wThXsKxmWUhS+qZ4sC6qNlB4bwkcLiXvdI1iIzaDuhopbUksfHc\nmudNuqD7mCfsXbE5VG2T+m9L97iYW/BKTUIhzBo/mss49oRcBd4R4+kLTtFGdu2i\nRndYAsIjSOyQ7IPwmHd8wuhtnlyDRYOTSCtFNKMWkDBSa0vEGHKyVZwmM4FMXPwB\nxQIDAQAB\n-----END PUBLIC KEY-----

# Micro services url
BLOG_SERVICE_URL=http://localhost:2100
EMAIL_SERVICE_URL=http://localhost:2200
ORDER_SERVICE_URL=http://localhost:2300
PAYMENT_SERVICE_URL=http://localhost:2400
PRODUCT_SERVICE_URL=http://localhost:2500
SCHOOL_PROGRAM_SERVICE_URL=http://localhost:2600
USER_SERVICE_URL=http://localhost:2700
SETTING_SERVICE_URL=http://localhost:2900

# Serving Static files
USER_SERVICE_IMAGE_BASE_URL=/v1/users/images


# Token config
ACCESS_TOKEN_LIFE=Math.floor(Date.now() / 1000) + (60 * 60) # One hour for expiration time
REFRESH_TOKEN_LIFE=86400 #One day for expiration time

# Redis db
REDIS_DB_HOST=127.0.0.1
REDIS_DB_PORT=6379
REDIS_DB_USER=valentin
REDIS_DB_PASSWORD=phpuser

# Authentication db
MONGODB_DB_HOST=127.0.0.1
MONGODB_DB_PORT=27017
MONGODB_DB_USER=valentin
MONGODB_DB_PASSWORD=password
MONGODB_DB_NAME=Authentication

# Swagger documentation
SWAGGER_BASE_URL=/v1/docs
SWAGGER_AUTH_BASE_URL=/v1/auth/docs