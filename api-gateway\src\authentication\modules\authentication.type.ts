export default interface AuthenticationType {
  _id: string;
  name: string,
  username: string;
  last_name: string;
  first_name: string;
  email: string;
  gender: string;
  image: string,
  role: string | RoleType;
  roles: Array<RoleType>;
}

interface RoleType {
  _id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface GenerateTokenType {
  accessToken: string;
  refreshToken: string;
}
