{"product": {"productNotFound": "Ce produit n'existe pas", "invalidProductId": "L'identifiant du produit n'est pas valide", "categoryAlreadyAssigned": "Cette catégorie est déjà attribué à ce produit.", "tagAlreadyAssigned": "Cette étiquette est déjà attribué à ce produit.", "notHaveThisCategory": "Ce produit n'a pas cette catégorie.", "notHaveThisTag": "Ce produit n'a pas cette étiquette."}, "category": {"categoryNotFound": "<PERSON>tte catégorie n'existe pas", "invalidCategoryId": "L'identifiant de la catégorie n'est pas valide"}, "tag": {"tagNotFound": "Cette étiquette n'existe pas", "invalidTagId": "L'identifiant de l'étiquette n'est pas valide"}, "coupon": {"couponNotFound": "Ce coupon n'existe pas", "invalidCouponId": "L'identifiant de ce coupon n'est pas valide"}, "attribute": {"attributeNotFound": "Cet attribut n'existe pas", "invalidAttributeId": "L'identifiant de cet attribut n'est pas valide"}, "extra": {"extraNotFound": "Cet extra n'existe pas", "invalidExtraId": "L'identifiant de cet extra n'est pas valide"}, "unauthorize": {"noToken": "<PERSON><PERSON> de jeton", "invalidToken": "Jeton non valide", "invalidAdminToken": "<PERSON><PERSON> d'administration invalide", "invalidSellerToken": "Jeton de vendeur invalide", "invalidAdminOrSeller": "Jeton d'administrateur ou de vendeur invalide", "invalidRefreshToken": "Jeton de rafraîchissement invalide", "noRefreshToken": "Pas de jeton de rafraîchissement"}, "config": {"cache": {"redis": {"redisConnectionTo": "La connexion Redis à", "failed": "a échoué"}}}, "others": {"routeNotFound": "Itinéraire non trouvé"}}