import config from "../../../config";

export default {
  explorer: true,
  swaggerOptions: {
    validatorUrl: null,
    urls: [
      {
        url: `${config.authenticationServiceUrl}/v1/auth/docs.json`,
        name: "Authentication service",
      },
      {
        url: `${config.blogServiceUrl}/v1/blogs/docs.json`,
        name: "Blog service",
      },
      {
        url: `${config.emailServiceUrl}/v1/emails/docs.json`,
        name: "Email service",
      },
      {
        url: `${config.orderServiceUrl}/v1/orders/docs.json`,
        name: "Order service",
      },
      {
        url: `${config.paymentServiceUrl}/v1/payments/docs.json`,
        name: "Payment service",
      },
      {
        url: `${config.productServiceUrl}/v1/products/docs.json`,
        name: "Product service",
      },
      // {
      //   url: `${config.schoolProgramServiceUrl}/v1/school-programs/docs-json`,
      //   name: "School program service",
      // },
      {
        url: `${config.userServiceUrl}/v1/users/docs.json`,
        name: "User service",
      },
      {
        url: `${config.settingServiceUrl}/v1/settings/docs.json`,
        name: "Setting service",
      },
    ],
  },
};
