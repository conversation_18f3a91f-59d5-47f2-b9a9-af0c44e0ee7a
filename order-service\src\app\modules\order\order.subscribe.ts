import { payOrderEmailTemplate } from "../../../resources/email/order-receive";
import DBManager from "../../../core/db";
import rabbitmqManager from "../../../core/rabbitmq";
import orderService from "./order.service";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-09-16
 *
 * Class OrderSubscribe
 */
class OrderSubscribe {
  /**
   * Update order payment status
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-09-10
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async updateOrderPaymentStatus(): Promise<void> {
    try {
      const { channel, queue } = await rabbitmqManager.setupQueue(
        "eluxe.payment.updateOrderPaymentStatus",
        "updateOrderPaymentStatus",
        "updateOrderPaymentStatusQueue"
      );

      channel.consume(queue, async (msg: any) => {
        try {
          const data = JSON.parse(msg.content);
          const dbManager = new DBManager();

          // Connect to the database
          const dbConnection = await dbManager.asyncOnConnect();

          await this.handleUpdateOrderPaymentStatus(data, dbConnection);
        } catch (error) {
          console.error("Message processing failed:", error);
        } finally {
          channel.ack(msg); // Always acknowledge the message
        }
      });

      // const dbManager = new DBManager();

      // const exchangeName = "eluxe.payment.updateOrderPaymentStatus";
      // const routingKey = "updateOrderPaymentStatus";
      // const queueName = "updateOrderPaymentStatusQueue";

      // await rabbitmqManager.createChannel();
      // const channel = rabbitmqManager.channel;
      // await channel.assertExchange(exchangeName, "direct");
      // const q = await channel.assertQueue(queueName);
      // await channel.bindQueue(q.queue, exchangeName, routingKey);

      // channel.consume(q.queue, (msg: any) => {
      //   const data: any = JSON.parse(msg.content);

      //   dbManager
      //     .asyncOnConnect()
      //     .then((dbConenction) => {
      //       orderService
      //         .getOrderById(data.message.order)
      //         .then((order: any) => {
      //           if (order !== null && order !== undefined) {
      //             orderService
      //               .patch(order._id, [
      //                 {
      //                   op: "replace",
      //                   path: "/is_paid",
      //                   value: data.message.is_paid,
      //                 },
      //                 {
      //                   op: "replace",
      //                   path: "/paid_at",
      //                   value: data.message.paid_at,
      //                 },
      //               ])
      //               .then((result) => {
      //                 console.log(result);

      //                 // Send confirmation email
      //                 rabbitmqManager
      //                   .publishMessage("logExchange", "sendMail", {
      //                     receivers: order.shipping_address.email,
      //                     subject: `New order ${order.invoice}`,
      //                     body: payOrderEmailTemplate(order),
      //                   })
      //                   .then((result) => {
      //                     console.log(result);
      //                   })
      //                   .catch((error) => {
      //                     console.log(error);
      //                   });

      //                 dbConenction.disconnect();
      //               })
      //               .catch((error) => {
      //                 console.log(error);
      //                 dbConenction.disconnect();
      //               });
      //           }
      //         })
      //         .catch((err) => {
      //           console.log(err);
      //           dbConenction.disconnect();
      //         });
      //     })
      //     .catch((err) => {
      //       console.log(err);
      //     });

      //   channel.ack(msg);
      // });
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Handles the update of an order's payment status.
   *
   * @param {any} data - The data containing the payment update information, including
   *                     the order ID, payment status, and payment date.
   * @param {any} dbConnection - The active database connection object to perform operations.
   * @returns {Promise<void>} - A promise that resolves when the operation is complete.
   *
   * @throws {Error} - Logs errors to the console if order retrieval, payment update,
   * or email sending fails.
   */
  private async handleUpdateOrderPaymentStatus(
    data: any,
    dbConnection: any
  ): Promise<void> {
    try {
      const order: any = await orderService.getOrderById(data.message.order);

      if (!order) {
        console.error("Order does not exist.");
        return;
      }

      await orderService.patch(order._id, [
        {
          op: "replace",
          path: "/is_paid",
          value: data.message.is_paid,
        },
        {
          op: "replace",
          path: "/paid_at",
          value: data.message.paid_at,
        },
      ]);

      await rabbitmqManager.publishMessage("logExchange", "sendMail", {
        receivers: order.shipping_address.email,
        subject: `New order ${order.invoice}`,
        body: payOrderEmailTemplate(order),
      });
    } catch (error) {
      console.error("Error handling Update Order Payment Status:", error);
    } finally {
      dbConnection.disconnect();
    }
  }
}

const orderSubscribe = new OrderSubscribe();
export default orderSubscribe;
