/* eslint-disable class-methods-use-this */
/* eslint-disable no-console */
import bodyParser from "body-parser";
import cors from "cors";
import { Application } from "express";
import DBManager from "./db";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-22-03
 *
 * Class AppConfig
 */
class AppConfig {
  private app;

  /**
   * Create a new UserController instance.
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-03-22
   *
   * @param {Application} app express application
   */
  constructor(app: Application) {
    process.on("unhandledRejection", (reason, p) => {
      console.log("Unhandled Rejection at: Promise", p, "reason:", reason);
      // application specific logging, throwing an error, or other logic here
    });
    this.app = app;
  }

  /**
   * Include the config.
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-22-03
   *
   * @return {void}
   */
  public includeConfig(): void {
    this.loadAppLevelConfig();
    this.loadExpressConfig();
  }

  /**
   * Load the App level config.
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-05-09
   *
   * @return {void}
   */
  public loadAppLevelConfig(): void {
    this.app.use(bodyParser.json({ limit: '50mb' }));
    this.app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
    // Faire confiance aux en-têtes du proxy
    // this.app.set('trust proxy', true);
    // this.app.use(upload.any()); // for parsing multipart/form-data requests
    this.app.use(cors());
  }

  /**
   * Load the Express config.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-05-09
   *
   * @return {void}
   */
  public loadExpressConfig(): void {
    new DBManager(this.app).setDBConnection();
  }
}

export default AppConfig;
