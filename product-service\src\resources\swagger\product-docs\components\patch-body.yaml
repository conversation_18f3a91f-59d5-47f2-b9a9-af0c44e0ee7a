components:
  schemas:
    PatchBody:
      type: array
      items:
        type: object
        description: A JSONPatch document as defined by RFC 6902 
        required:
          - "op"
          - "path"
        properties: 
          op: 
            type: string 
            description: The operation to be performed 
            enum:
              - "add"
              - "remove"
              - "replace"
              - "move"
              - "copy"
              - "test"
          path: 
            type: string 
            description: A JSON-Pointer 
          value: 
            type: object 
            description: The value to be used within the operations.
          from: 
            type: string 
            description: A string containing a JSON Pointer value.