export default interface EmailType {
  _id: string;
  message_id: string;
  sender_name: string;
  subject: string;
  body: string;
  accepted: Array<string>;
  rejected: Array<string>;
  envelope_time: number;
  message_time: number;
  message_size: number;
  response: string;
  envelope: Envelope;
  created_at: Date;
  updated_at: Date;
}

interface Envelope {
  from: string;
  to: Array<string>;
}
