/**
 * Script de Test de Connectivité API E-Luxe
 * Teste la connexion à l'API Gateway sur localhost:2000
 */

const testApiConnection = async () => {
  console.log('🔍 Test de connectivité API E-Luxe...\n');

  const baseUrl = 'http://localhost:2000';
  const endpoints = [
    '/v1/en/products',
    '/v1/en/categories',
    '/v1/en/setting/global',
    '/v1/en/setting/store/customization',
  ];

  for (const endpoint of endpoints) {
    const url = `${baseUrl}${endpoint}`;
    console.log(`🌐 Test: ${url}`);
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        timeout: 10000,
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS: ${endpoint} - Status: ${response.status}`);
        console.log(`   Data type: ${Array.isArray(data) ? 'Array' : typeof data}`);
        if (Array.isArray(data)) {
          console.log(`   Items count: ${data.length}`);
        }
      } else {
        console.log(`❌ ERROR: ${endpoint} - Status: ${response.status}`);
        const errorText = await response.text();
        console.log(`   Error: ${errorText.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`💥 NETWORK ERROR: ${endpoint}`);
      console.log(`   Error: ${error.message}`);
    }
    
    console.log(''); // Ligne vide
  }

  console.log('🏁 Test terminé.\n');
  console.log('📋 Instructions:');
  console.log('1. Si tous les tests échouent: Vérifiez que l\'API Gateway tourne sur localhost:2000');
  console.log('2. Si certains échouent: Vérifiez les routes dans l\'API Gateway');
  console.log('3. Si tout fonctionne: L\'application mobile devrait marcher !');
};

// Exécuter le test
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testApiConnection();
} else {
  // Browser environment
  testApiConnection();
}

module.exports = { testApiConnection };
