import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { ELuxeColors } from '../../../theme/colors';

type ServicesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Services'>;

interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
  action?: () => void;
}

const ServicesScreen: React.FC = () => {
  const navigation = useNavigation<ServicesScreenNavigationProp>();

  const services: Service[] = [
    {
      id: 'personal-shopping',
      title: 'Personal Shopping',
      description: 'Get personalized style recommendations from our fashion experts',
      icon: 'person',
      features: [
        'One-on-one style consultation',
        'Curated product recommendations',
        'Virtual wardrobe planning',
        'Seasonal style updates',
      ],
      action: () => {
        // TODO: Navigate to personal shopping booking
        navigation.navigate('Contact');
      },
    },
    {
      id: 'premium-delivery',
      title: 'Premium Delivery',
      description: 'Fast, secure, and convenient delivery options',
      icon: 'local-shipping',
      features: [
        'Same-day delivery in select cities',
        'White-glove delivery service',
        'Scheduled delivery windows',
        'Package tracking and notifications',
      ],
    },
    {
      id: 'alterations',
      title: 'Alterations & Tailoring',
      description: 'Professional alterations for the perfect fit',
      icon: 'content-cut',
      features: [
        'Expert tailoring services',
        'Hemming and adjustments',
        'Custom fitting appointments',
        'Quality guarantee',
      ],
    },
    {
      id: 'styling',
      title: 'Virtual Styling',
      description: 'Online styling sessions with fashion professionals',
      icon: 'video-call',
      features: [
        'Video consultation sessions',
        'Style assessment and advice',
        'Outfit coordination tips',
        'Wardrobe organization help',
      ],
    },
    {
      id: 'concierge',
      title: 'Concierge Service',
      description: 'Premium customer service for VIP members',
      icon: 'star',
      features: [
        'Dedicated account manager',
        'Priority customer support',
        'Exclusive access to new collections',
        'Special event invitations',
      ],
    },
    {
      id: 'gift-wrapping',
      title: 'Gift Services',
      description: 'Beautiful gift wrapping and personalized messages',
      icon: 'card-giftcard',
      features: [
        'Luxury gift wrapping',
        'Personalized gift messages',
        'Gift receipt options',
        'Special occasion packaging',
      ],
    },
  ];

  const renderServiceCard = (service: Service) => (
    <View key={service.id} style={styles.serviceCard}>
      <View style={styles.serviceHeader}>
        <View style={styles.serviceIconContainer}>
          <Icon name={service.icon} size={32} color={ELuxeColors.primary} />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceTitle}>{service.title}</Text>
          <Text style={styles.serviceDescription}>{service.description}</Text>
        </View>
      </View>

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>What's Included:</Text>
        {service.features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.featureText}>{feature}</Text>
          </View>
        ))}
      </View>

      {service.action && (
        <TouchableOpacity style={styles.serviceButton} onPress={service.action}>
          <Text style={styles.serviceButtonText}>Learn More</Text>
          <Icon name="arrow-forward" size={16} color="#007AFF" />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderContactSection = () => (
    <View style={styles.contactSection}>
      <Text style={styles.contactTitle}>Need Help with Our Services?</Text>
      <Text style={styles.contactSubtitle}>
        Our customer service team is here to assist you with any questions about our services.
      </Text>
      
      <View style={styles.contactButtons}>
        <TouchableOpacity
          style={styles.contactButton}
          onPress={() => navigation.navigate('Contact')}
        >
          <Icon name="chat" size={20} color="#FFFFFF" />
          <Text style={styles.contactButtonText}>Contact Us</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.contactButton, styles.phoneButton]}
          onPress={() => Linking.openURL('tel:+15551234567')}
        >
          <Icon name="phone" size={20} color="#007AFF" />
          <Text style={[styles.contactButtonText, styles.phoneButtonText]}>Call Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Our Services</Text>
        <Text style={styles.headerSubtitle}>
          Discover our premium services designed to enhance your shopping experience
        </Text>
      </View>

      {/* Services List */}
      <View style={styles.servicesContainer}>
        {services.map(renderServiceCard)}
      </View>

      {/* Contact Section */}
      {renderContactSection()}

      {/* Benefits Section */}
      <View style={styles.benefitsSection}>
        <Text style={styles.benefitsTitle}>Why Choose Our Services?</Text>
        
        <View style={styles.benefitItem}>
          <Icon name="verified" size={24} color="#4CAF50" />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Expert Professionals</Text>
            <Text style={styles.benefitDescription}>
              Our team consists of certified fashion experts and stylists
            </Text>
          </View>
        </View>

        <View style={styles.benefitItem}>
          <Icon name="schedule" size={24} color="#FF9500" />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Flexible Scheduling</Text>
            <Text style={styles.benefitDescription}>
              Book appointments that fit your busy lifestyle
            </Text>
          </View>
        </View>

        <View style={styles.benefitItem}>
          <Icon name="security" size={24} color="#007AFF" />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Satisfaction Guarantee</Text>
            <Text style={styles.benefitDescription}>
              100% satisfaction guarantee on all our services
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  servicesContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  serviceCard: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    minHeight: 280, // Hauteur minimale uniforme
    justifyContent: 'space-between', // Distribution équitable du contenu
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  serviceIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  featuresContainer: {
    marginBottom: 16,
  },
  featuresTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
  serviceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 8,
  },
  serviceButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
    marginRight: 8,
  },
  contactSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 8,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  contactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
    textAlign: 'center',
  },
  contactSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  contactButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  phoneButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  contactButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  phoneButtonText: {
    color: '#007AFF',
  },
  benefitsSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 20,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  benefitContent: {
    flex: 1,
    marginLeft: 16,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default ServicesScreen;
