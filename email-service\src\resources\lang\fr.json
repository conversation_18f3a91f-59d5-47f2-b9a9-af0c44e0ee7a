{"user": {"login": {"passwordNotFound": "Le mot de passe ne peut pas être vide.", "userNameNotFound": "Le nom d'utilisateur ne peut pas être vide.", "userLastNameNotFound": "Le nom de famille de l'utilisateur ne peut pas être vide.", "userIdNotFound": "L'identifiant de l'utilisateur ne peut pas être vide.", "emailNotFound": "Le courrier électronique ne peut pas être vide.", "genderNotFound": "Le genre ne peut pas être vide.", "userNameDetailFailed": "Échec de la saisie des données de l'utilisateur", "userRegistrationOk": "L'enregistrement de l'utilisateur a été effectué avec succès.", "userRegistrationFailed": "L'enregistrement de l'utilisateur n'a pas abouti.", "userLoginOk": "Utilisateur connecté.", "userLoginFailed": "Nom d'utilisateur ou mot de passe incorrect."}, "unauthorize": {"noToken": "<PERSON><PERSON> de jeton", "invalidToken": "Jeton non valide", "invalidAdminToken": "<PERSON><PERSON> d'administration invalide", "invalidSellerToken": "Jeton de vendeur invalide", "invalidAdminOrSeller": "Jeton d'administrateur ou de vendeur invalide", "invalidRefreshToken": "Jeton de rafraîchissement invalide", "noRefreshToken": "Pas de jeton de rafraîchissement"}, "profile": {"userNotFound": "L'utilisateur n'existe pas.", "invalidUserId": "Invalid user id."}, "update": {"userNotFound": "L'utilisateur n'existe pas.", "incorrectCurrentPassword": "Mot de passe actuel incorrect", "currPawdNewPwdNotdifferent": "Le mot de passe actuel et le nouveau mot de passe doivent être différents"}, "delete": {"cannotDeleteAdmin": "Impossible de supprimer l'utilisateur Admin"}, "others": {"invalidUserId": "Identifiant de l'utilisateur invalide."}, "assign": {"roleAlreadyAssigned": "Ce rôle est déjà attribué à cet utilisateur."}, "unassign": {"notHaveThisRole": "L'utilisateur n'a pas ce rôle."}}, "others": {"routeNotFound": "Itinéraire non trouvé"}, "role": {"show": {"roleNotFound": "Le rôle ne s'arrête pas."}, "others": {"invalidRoleId": "L'identifiant du rôle n'est pas valide."}}, "gender": {"show": {"genderNotFound": "Le genre n'existe pas."}, "others": {"invalidGenderId": "Identifiant de genre invalide."}}, "config": {"cache": {"redis": {"redisConnectionTo": "La connexion Redis à", "failed": "a échoué"}}}}