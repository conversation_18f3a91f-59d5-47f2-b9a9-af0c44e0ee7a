components:
  schemas:
    StripePaymentIntent:
      type: object
      properties:
        _id:
          type: string
        id:
          type: string
        object:
          type: string
        amount:
          type: number
        amount_capturable:
          type: number
        amount_details:
          type: object
          properties:
            tip:
              type: object
        amount_received:
          type: number
        application:
          type: string
        application_fee_amount:
          type: string
        automatic_payment_methods:
          type: object
          properties:
            enabled:
              type: boolean
        canceled_at:
          type: number
        cancellation_reason:
          type: string
        capture_method:
          type: string
        client_secret:
          type: string
        confirmation_method:
          type: string
        created:
          type: number
        currency:
          type: string
        customer:
          type: string
        description:
          type: string
        invoice:
          type: string
        last_payment_error:
          type: string
        latest_charge:
          type: string
        livemode:
          type: boolean
        metadata:
          type: object
          properties:
            order_id:
              type: string
        next_action:
          type: string
        on_behalf_of:
          type: string
        payment_method:
          type: string
        payment_method_options:
          type: object
          properties:
            card:
              type: object
              properties:
                installments:
                  type: string
                mandate_options:
                  type: string
                network:
                  type: string
                request_three_d_secure:
                  type: string
        payment_method_types:
          type: array
          items:
        processing:
          type: string
        receipt_email:
          type: string
        review:
          type: string
        setup_future_usage:
          type: string
        shipping:
          type: string
        statement_descriptor:
          type: string
        statement_descriptor_suffix:
          type: string
        status:
          type: string
        transfer_data:
          type: string
        transfer_group:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time