import { Request, Response } from "express";
import customResponse from "../../utils/custom-response.util";
import statusCode from "../../utils/status-code.util";
import errorNumbers from "../../utils/error-numbers.util";
import emailService from "./email.service";
import validator from "../../utils/validator.util";
import { Errors } from "validatorjs";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-22
 *
 * Class EmailController
 */
class EmailController {
  /**
   * Send email
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-23
   *
   * @param {Request} req the http request
   * @param {Response} res the http response
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async sendEmail(req: Request, res: Response): Promise<void> {
    const validationRule = {
      senderName: "required",
      senderEmail: "required|email",
      subject: "required",
      body: "required",
    };

    const reqBody = req.body;

    await validator
      .validator(
        reqBody,
        validationRule,
        {},
        (err: Errors, status: boolean) => {
          if (!status) {
            const response = {
              status: statusCode.httpPreconditionFailed,
              errNo: errorNumbers.validator,
              errMsg: err.errors,
            };

            return customResponse.error(response, res);
          } else {
            emailService
              .sendEmail(reqBody)
              .then((result) => {
                const response = {
                  status: statusCode.httpOk,
                  data: result,
                };

                return customResponse.success(response, res);
              })
              .catch((error) => {
                const response = {
                  status: error?.status || statusCode.httpInternalServerError,
                  errNo: errorNumbers.genericError,
                  errMsg: error?.message || error,
                };

                return customResponse.error(response, res);
              });
          }
        }
      )
      .catch((error) => {
        const response = {
          status: error?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: error?.message || error,
        };

        return customResponse.error(response, res);
      });
  }
}

const emailController = new EmailController();
export default emailController;
