import { Application } from "express";
import swaggerUi from "swagger-ui-express";
import swaggerMicroservicesOptions from "../../resources/swagger/microservices-docs";
import statusCode from "../utils/status-code.util";
import errorNumbers from "../utils/error-numbers.util";
import i18n from "../../core/i18n";
import customResponse from "../utils/custom-response.util";
import routesGrouping from "../utils/routes-grouping.util";
import authenticationModuleRoutes from "./authentication-module.route";
import userServiceRoutes from "./user-service.route";
import setLocale from "../middlewares/set-locale.middleware";
import productServiceRoutes from "./product-service.route";
import orderServiceRoutes from "./order-service.route";
import emailServiceRoutes from "./email-service.route";
import paymentServiceRoutes from "./payment-service.route";
import blogServiceRoutes from "./blog-service.route";
import schoolProgramServiceRoutes from "./school-program-service.route";
import settingServiceRoutes from "./setting-service.route";

/**
 * <AUTHOR> <PERSON>g<PERSON> <<EMAIL>>
 * @since 2023-03-26
 *
 * Class AppRoutes
 */
class AppRoutes {
  private app: Application;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-03-26
   *
   * @param {Application} app express application
   */
  constructor(app: Application) {
    this.app = app;
  }

  /**
   * Creating app Routes starts
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-26-03
   *
   * @returns {void}
   */
  public appRoutes(): void {
    this.app.use(
      "/v1",
      routesGrouping.group((router) => {
        router.use(
          "/:lang",
          setLocale.setLocale,
          routesGrouping.group((router) => {
            /******************************************************************
             * Includes all microservices routes here
             ******************************************************************/

            // Authentication module routes
            router.use(authenticationModuleRoutes.authenticationRoutes());

            // User service routes
            router.use(userServiceRoutes.userServiceRoutes());

            // Product service routes
            router.use(productServiceRoutes.productServiceRoutes());

            // Order service routes
            router.use(orderServiceRoutes.orderServiceRoutes());

            // Email service routes
            router.use(emailServiceRoutes.emailServiceRoutes());

            // Payment service routes
            router.use(paymentServiceRoutes.paymentServiceRoutes());

            // Blog service routes
            router.use(blogServiceRoutes.blogServiceRoutes());

            // School program service routes
            router.use(schoolProgramServiceRoutes.schoolProgramServiceRoutes());

            // Setting service routes
            router.use(settingServiceRoutes.settingServiceRoutes());
          })
        );
      })
    );

    // Swagger documentation routes
    this.app.use(
      "/v1",
      routesGrouping.group((router) => {
        // All microservice swagger documentation route
        router.use(
          "/docs",
          swaggerUi.serveFiles(undefined, swaggerMicroservicesOptions),
          swaggerUi.setup(undefined, swaggerMicroservicesOptions)
        );

        // Authentication swagger documentation
        router.use(authenticationModuleRoutes.authenticationDocsRoutes());

        // User service static files
        router.use(userServiceRoutes.userServiceImageRoutes());
      })
    );

    // error handler for not found router
    this.app.all("*", (req, res) => {
      const response = {
        status: statusCode.httpNotFound,
        errNo: errorNumbers.resourceNotFound,
        errMsg: i18n.__("others.routeNotFound"),
      };

      return customResponse.error(response, res);
    });
  }

  /**
   * Load routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-03-26
   *
   * @returns {void}
   */
  public routesConfig(): void {
    this.appRoutes();
  }
}

export default AppRoutes;
