components:
  schemas:
    Post:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
          description: The post's name.
          example: Backpack
        image:
          description: <PERSON>'s image url
          type: string
        description:
          description: <PERSON>'s description
          type: string
        rating:
          description: Post's rating. Value between 1 and 5
          type: number
          example: 0
        numReviews:
          description: Number of reviews
          type: number
          example: 0
        reviews:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                description: Name of reviewer
                example: <PERSON>
              email:
                type: string
                description: Email of reviewer
                example: <EMAIL>
              comment:
                type: string
                description: Comment of reviewer
                example: An excellent post
              rating:
                type: number
                description: Post's rating. Value between 1 and 5
                example: 1
        tags:
          type: array
          items:
            type: string
            description: Tag's id
        categories:
          type: array
          items:
            type: string
            description: Category's id
        related_posts:
          type: array
          items:
            type: string
            description: Related post's id
        status:
          type: string
          description: Post status
        user:
          description: User id
          type: string
        publish_date:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time