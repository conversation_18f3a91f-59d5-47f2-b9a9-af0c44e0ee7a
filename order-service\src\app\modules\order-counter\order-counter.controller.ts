import { Request, Response } from "express";
import i18n from "../../../core/i18n";
import customResponse from "../../utils/custom-response.util";
import statusCode from "../../utils/status-code.util";
import errorNumbers from "../../utils/error-numbers.util";
import orderCounterService from "./order-counter.service";

/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2023-08-17
 *
 * Class OrderCounterController
 */
class OrderCounterController {
  /**
   * Get order counter details handler
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-08-17
   *
   * @param {Request} req the http request
   * @param {Response} res the http response
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async getSequenceNextValue(
    req: Request,
    res: Response
  ): Promise<void> {
    orderCounterService
      .getSequenceNextValue("orderId")
      .then((result) => {
        if (result === null || result === undefined) {
          const response = {
            status: statusCode.httpNotFound,
            errNo: errorNumbers.resourceNotFound,
            errMsg: i18n.__("orderCounter.orderCounterNotFound"),
          };

          return customResponse.error(response, res);
        }
        else {
          const response = {
            status: statusCode.httpOk,
            data: result,
          };

          return customResponse.success(response, res);
        }
      })
      .catch((error) => {
        const response = {
          status: error?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: error?.message || error,
        };

        return customResponse.error(response, res);
      });
  }

  /**
   * Create an order counter
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-17
   *
   * @param {Request} req the http request
   * @param {Response} res the http response
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async init(req: Request, res: Response): Promise<void> {
    orderCounterService
      .init()
      .then((result: any) => {
        const response = {
          status: statusCode.httpOk,
          data: result,
        };

        return customResponse.success(response, res);
      })
      .catch((error) => {
        const response = {
          status: error?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: error?.message || error,
        };

        return customResponse.error(response, res);
      });
  }
}

const orderCounterController = new OrderCounterController();
export default orderCounterController;