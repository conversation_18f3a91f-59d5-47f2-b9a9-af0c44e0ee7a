{"post": {"postNotFound": "Post does not exits", "invalidPostId": "Invalid post id", "categoryAlreadyAssigned": "This category is already assigned to this post", "tagAlreadyAssigned": "This tag is already assigned to this post", "notHaveThisCategory": "This post does not have this category.", "notHaveThisTag": "This post does not have this tag."}, "category": {"categoryNotFound": "Category does not exits", "invalidCategoryId": "Invalid category id"}, "tag": {"tagNotFound": "Tag does not exits", "invalidTagId": "Invalid tag id"}, "unauthorize": {"noToken": "No token", "invalidToken": "Invalid token", "invalidAdminToken": "Invalid admin token", "invalidSellerToken": "Invalid seller token", "invalidAdminOrSeller": "Invalid admin or seller token", "invalidRefreshToken": "Invalid refresh token", "noRefreshToken": "No refresh token"}, "config": {"cache": {"redis": {"redisConnectionTo": "Redis connection to", "failed": "failed"}}}, "others": {"routeNotFound": "Route not found"}}