import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApiConfig, DEFAULT_HEADERS, API_TIMEOUTS, SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, buildUrl } from '../../config/ApiConfig';

export interface ApiResponse<T = any> {
  data: T;
  message: string;
  success: boolean;
  status: number;
}

export class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    // Configuration basée sur l'environnement (compatible avec l'app web)
    const apiConfig = getApiConfig();
    this.baseURL = apiConfig.apiGatewayUrl;

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: API_TIMEOUTS.DEFAULT,
      headers: DEFAULT_HEADERS,
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // Add language header (compatible avec l'app web)
        const language = await AsyncStorage.getItem('app_language') || DEFAULT_LANGUAGE;
        if (SUPPORTED_LANGUAGES.includes(language)) {
          config.headers['Accept-Language'] = language;
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, try to refresh
          const refreshToken = await AsyncStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              const response = await this.refreshToken(refreshToken);
              await AsyncStorage.setItem('auth_token', response.data.token);
              
              // Retry original request
              error.config.headers.Authorization = `Bearer ${response.data.token}`;
              return this.client.request(error.config);
            } catch (refreshError) {
              // Refresh failed, redirect to login
              await this.clearTokens();
              // You might want to emit an event here to redirect to login
            }
          }
        }
        
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private async refreshToken(refreshToken: string): Promise<AxiosResponse> {
    return axios.post(`${this.baseURL}/auth/refresh`, {
      refreshToken,
    });
  }

  private async clearTokens(): Promise<void> {
    await AsyncStorage.multiRemove(['auth_token', 'refresh_token']);
  }

  private handleError(error: any): Error {
    // Logs uniquement en développement
    if (__DEV__) {
      console.error('🚨 ApiClient Error Details:', {
        hasResponse: !!error.response,
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
    }

    if (error.response) {
      // Server responded with error status
      const responseData = error.response.data;

      // Format spécifique de l'API Gateway E-Luxe
      let message = 'Erreur API inconnue';

      if (responseData?.data?.errMsg) {
        // Format E-Luxe: { status: "FAILED", data: { errNo: 7, errMsg: "message" } }
        message = responseData.data.errMsg;
      } else if (responseData?.message) {
        // Format standard: { message: "error message" }
        message = responseData.message;
      } else if (responseData?.errMsg) {
        // Format alternatif: { errMsg: "error message" }
        message = responseData.errMsg;
      } else if (error.response.statusText) {
        // Fallback sur statusText
        message = error.response.statusText;
      }

      if (__DEV__) {
        console.error(`❌ API Error [${error.response.status}]:`, message);
      }
      return new Error(`API Error: ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      if (__DEV__) {
        console.error('❌ Network Error: No response from server');
      }
      return new Error('Network Error: No response from server');
    } else {
      // Something else happened
      if (__DEV__) {
        console.error('❌ Request Error:', error.message);
      }
      return new Error(`Request Error: ${error.message}`);
    }
  }

  // HTTP Methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // File upload
  async uploadFile<T>(url: string, file: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const uploadConfig = {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    };

    const response = await this.client.post(url, file, uploadConfig);
    return response.data;
  }

  // Méthodes avec support de langue automatique pour API v1/{lang}
  async getWithLang<T>(endpoint: string, params?: Record<string, string>, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const language = await AsyncStorage.getItem('app_language') || DEFAULT_LANGUAGE;
    const url = buildUrl(endpoint, params, language);
    return this.get<T>(url, config);
  }

  async postWithLang<T>(endpoint: string, data?: any, params?: Record<string, string>, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const language = await AsyncStorage.getItem('app_language') || DEFAULT_LANGUAGE;
    const url = buildUrl(endpoint, params, language);
    return this.post<T>(url, data, config);
  }

  async putWithLang<T>(endpoint: string, data?: any, params?: Record<string, string>, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const language = await AsyncStorage.getItem('app_language') || DEFAULT_LANGUAGE;
    const url = buildUrl(endpoint, params, language);
    return this.put<T>(url, data, config);
  }

  async deleteWithLang<T>(endpoint: string, params?: Record<string, string>, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const language = await AsyncStorage.getItem('app_language') || DEFAULT_LANGUAGE;
    const url = buildUrl(endpoint, params, language);
    return this.delete<T>(url, config);
  }
}

// Singleton instance
export const apiClient = new ApiClient();
