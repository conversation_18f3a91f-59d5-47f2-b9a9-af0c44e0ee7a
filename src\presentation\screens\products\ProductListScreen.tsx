import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Modal,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { getProductsAsync } from '../../store/slices/productSlice';
import { setFilter, clearProducts } from '../../store/slices/productSlice';
import { RootStackParamList } from '../../navigation/AppNavigator';
import ProductCard from '../../components/ProductCard';

type ProductListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ProductList'>;
type ProductListScreenRouteProp = {
  params: {
    categoryId?: string;
    title?: string;
  };
};

const ProductListScreen: React.FC = () => {
  const navigation = useNavigation<ProductListScreenNavigationProp>();
  const route = useRoute<ProductListScreenRouteProp>();
  const dispatch = useAppDispatch();
  
  const { categoryId, title } = route.params;
  const { products, isLoading, filter, pagination } = useAppSelector((state) => state.product);
  
  const [refreshing, setRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showSortModal, setShowSortModal] = useState(false);

  // SUPPRIMÉ: Données mockées - utilisation exclusive de l'API
  // Les produits viennent de Redux store via l'API

  const sortOptions = [
    { label: 'Newest', value: 'newest' },
    { label: 'Price: Low to High', value: 'price_asc' },
    { label: 'Price: High to Low', value: 'price_desc' },
    { label: 'Name: A to Z', value: 'name_asc' },
    { label: 'Name: Z to A', value: 'name_desc' },
    { label: 'Rating', value: 'rating' },
  ];

  useEffect(() => {
    navigation.setOptions({
      title: title || 'Products',
    });
    
    loadProducts();
  }, [categoryId, title]);

  useEffect(() => {
    loadProducts();
  }, [filter]);

  const loadProducts = async () => {
    try {
      // Appel API réel avec 48 produits par page et filtres fonctionnels
      await dispatch(getProductsAsync({
        ...filter,
        category: categoryId,
        page: 1,
        limit: 48, // 48 produits par page comme demandé
        // Filtres additionnels comme le client web
        minPrice: filter.priceRange?.min,
        maxPrice: filter.priceRange?.max,
        rating: filter.rating,
        inStock: filter.inStock,
        brands: filter.brands,
        colors: filter.colors,
      })).unwrap();
    } catch (error) {
      console.error('Failed to load products:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    dispatch(clearProducts());
    await loadProducts();
    setRefreshing(false);
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const handleAddToCart = (product: any) => {
    // TODO: Implement add to cart
    console.log('Add to cart:', product);
  };

  const handleToggleWishlist = (product: any) => {
    // TODO: Implement wishlist toggle
    console.log('Toggle wishlist:', product);
  };

  const handleSortChange = (sortBy: string) => {
    dispatch(setFilter({ sortBy: sortBy as any }));
    setShowSortModal(false);
  };

  const handleFilterPress = () => {
    setShowFilters(true);
  };

  const renderProductItem = ({ item, index }: { item: any; index: number }) => (
    <View style={[styles.productContainer, index % 2 === 1 && styles.productContainerRight]}>
      <ProductCard
        product={item}
        onPress={() => handleProductPress(item.id)}
        onAddToCart={() => handleAddToCart(item)}
        onToggleWishlist={() => handleToggleWishlist(item)}
        isInWishlist={false}
        showAddToCart={true}
      />
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <Text style={styles.resultCount}>
          {products.length} products found
        </Text>
      </View>
      
      <View style={styles.headerRight}>
        <TouchableOpacity style={styles.headerButton} onPress={handleFilterPress}>
          <Icon name="filter-list" size={20} color="#666666" />
          <Text style={styles.headerButtonText}>Filter</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => setShowSortModal(true)}
        >
          <Icon name="sort" size={20} color="#666666" />
          <Text style={styles.headerButtonText}>Sort</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderSortModal = () => (
    <Modal
      visible={showSortModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowSortModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Sort by</Text>
            <TouchableOpacity onPress={() => setShowSortModal(false)}>
              <Icon name="close" size={24} color="#666666" />
            </TouchableOpacity>
          </View>
          
          {sortOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={styles.sortOption}
              onPress={() => handleSortChange(option.value)}
            >
              <Text
                style={[
                  styles.sortOptionText,
                  filter.sortBy === option.value && styles.selectedSortOption,
                ]}
              >
                {option.label}
              </Text>
              {filter.sortBy === option.value && (
                <Icon name="check" size={20} color="#007AFF" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="search-off" size={80} color="#C7C7CC" />
      <Text style={styles.emptyTitle}>No products found</Text>
      <Text style={styles.emptySubtitle}>
        Try adjusting your filters or search terms
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      {products.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={products}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id || item._id}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          columnWrapperStyle={styles.row}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          onEndReachedThreshold={0.1}
          onEndReached={() => {
            // TODO: Load more products
            console.log('Load more products');
          }}
        />
      )}
      
      {renderSortModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerLeft: {
    flex: 1,
  },
  resultCount: {
    fontSize: 14,
    color: '#666666',
  },
  headerRight: {
    flexDirection: 'row',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
  },
  headerButtonText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 4,
  },
  listContainer: {
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  productContainer: {
    flex: 1,
    marginHorizontal: 4,
    marginVertical: 4,
  },
  productContainerRight: {
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  sortOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sortOptionText: {
    fontSize: 16,
    color: '#000000',
  },
  selectedSortOption: {
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default ProductListScreen;
