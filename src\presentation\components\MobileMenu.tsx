/**
 * Composant MobileMenu - Mobile E-Luxe 1.0
 * Reproduction EXACTE du composant MobileMenu.tsx du client web
 * Menu latéral avec navigation et sélecteur de langue
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  SafeAreaView,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ELuxeColors, ComponentColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import { ELuxeIcon } from '../../components/common/Icon';
import { useAppSelector } from '../store/store';
// import { useTranslations, useLanguage } from '../../../hooks/useTranslation';

// Menu principal (EXACTEMENT comme MainMenu.json du client web)
const MainMenu = [
  { url: 'Home', i18n: 'home', icon: 'home' },
  { url: 'Shop', i18n: 'shop', icon: 'store' },
  { url: 'Categories', i18n: 'categories', icon: 'category' },
  { url: 'Products', i18n: 'products', icon: 'inventory' },
  { url: 'Deals', i18n: 'deals', icon: 'local-offer' },
  { url: 'Cart', i18n: 'cart', icon: 'shopping-cart' },
  { url: 'Wishlist', i18n: 'wishlist', icon: 'favorite' },
  { url: 'Profile', i18n: 'account', icon: 'person' },
  { url: 'About', i18n: 'about', icon: 'info' },
  { url: 'Contact', i18n: 'contact', icon: 'contact-mail' },
  { url: 'FAQ', i18n: 'faq', icon: 'help' },
  { url: 'Services', i18n: 'services', icon: 'room-service' },
  { url: 'TrackOrder', i18n: 'track_order', icon: 'local-shipping' },
];

interface MobileMenuProps {
  isVisible: boolean;
  onClose: () => void;
}

/**
 * MobileMenu principal - Reproduction exacte du client web
 */
const MobileMenu: React.FC<MobileMenuProps> = ({ isVisible, onClose }) => {
  const navigation = useNavigation();

  // Récupération du logo et des settings depuis le store (comme le client web)
  const { globalSetting, storeCustomizationSetting } = useAppSelector(state => state.setting);

  // Logo priorité: navbar.logo > logo > fallback (comme HeaderSticky.tsx du client web)
  const logo = storeCustomizationSetting?.navbar?.logo ||
               storeCustomizationSetting?.logo ||
               globalSetting?.logo || '';
  const shopName = globalSetting?.shop_name || 'E-Luxe';

  console.log('🎨 MobileMenu - Logo debug:', {
    navbarLogo: storeCustomizationSetting?.navbar?.logo,
    settingLogo: storeCustomizationSetting?.logo,
    globalLogo: globalSetting?.logo,
    finalLogo: logo,
    shopName: shopName
  });

  // Version simplifiée sans i18n
  const t = (key: string) => {
    const translations: Record<string, string> = {
      home: 'Home',
      categories: 'Categories',
      products: 'Products',
      cart: 'Cart',
      account: 'Account',
      settings: 'Settings',
      language: 'Language',
    };
    return translations[key] || key;
  };

  const currentLanguage = 'en';
  const changeLanguage = (lang: string) => console.log('Changement langue:', lang);
  const supportedLanguages = ['en', 'fr'];
  const getLanguageName = (lang: string) => lang === 'fr' ? 'Français' : 'English';

  // Fonction pour obtenir les icônes avec ELuxeIcon
  const getMenuIcon = (iconName: string) => {
    return (
      <ELuxeIcon
        icon={iconName as any}
        size="md"
        color={ELuxeColors.primary} // Couleur or E-Luxe pour les icônes
      />
    );
  };
  
  const [isActive, setIsActive] = useState({
    status: false,
    key: '',
  });

  // Gestion du clic sur menu (comme le client web)
  const handleClick = (key: string) => {
    if (isActive.key === key) {
      setIsActive({
        status: false,
        key: '',
      });
    } else {
      setIsActive({
        status: true,
        key,
      });
    }
  };

  // Navigation vers une page
  const handleNavigation = (url: string) => {
    console.log('🧭 Navigation vers:', url);
    onClose();
    
    // Navigation selon l'URL (comme le client web)
    switch (url) {
      case 'Home':
        navigation.navigate('Home' as never);
        break;
      case 'Categories':
        navigation.navigate('Categories' as never);
        break;
      case 'Products':
        navigation.navigate('ProductList' as never, { categoryId: null, title: 'All Products' } as never);
        break;
      case 'Cart':
        navigation.navigate('Cart' as never);
        break;
      case 'Profile':
        navigation.navigate('Profile' as never);
        break;
      case 'Settings':
        navigation.navigate('Settings' as never);
        break;
      default:
        console.log('URL non gérée:', url);
    }
  };

  // Changement de langue (comme LanguageSwitcherTwo du client web)
  const handleLanguageChange = async (language: string) => {
    console.log('🌍 Changement de langue:', language);
    await changeLanguage(language);
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
      statusBarTranslucent={true}
      hardwareAccelerated={true}
    >
      <View style={styles.overlay}>
        <SafeAreaView style={styles.container}>
          {/* Header du menu (comme mean-bar) */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              {logo && logo.trim() !== '' ? (
                <Image
                  source={{ uri: logo }}
                  style={styles.logo}
                  resizeMode="contain"
                  onError={(error) => {
                    console.log('⚠️ Erreur chargement logo dans menu mobile:', logo, error);
                  }}
                  onLoad={() => {
                    console.log('✅ Logo chargé avec succès dans menu mobile:', logo);
                  }}
                />
              ) : (
                <View style={styles.logoPlaceholder}>
                  <Text style={styles.logoText}>E-L</Text>
                </View>
              )}
              <Text style={styles.headerTitle}>{shopName}</Text>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              activeOpacity={0.7}
            >
              <ELuxeIcon
                icon="close"
                size="md"
                color={ELuxeColors.textPrimary}
              />
            </TouchableOpacity>
          </View>

          {/* Navigation principale (comme mean-nav) */}
          <ScrollView style={styles.content}>
            <View style={styles.menuList}>
              {MainMenu.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.menuItem}
                  onPress={() => handleNavigation(item.url)}
                  activeOpacity={0.7}
                  android_ripple={{
                    color: 'rgba(255, 255, 255, 0.2)',
                    borderless: false,
                  }}
                >
                  <View style={styles.menuItemContent}>
                    <View style={styles.menuIcon}>
                      {getMenuIcon(item.icon)}
                    </View>
                    <Text style={styles.menuText}>
                      {t(item.i18n) || item.title || item.name || 'Menu Item'}
                    </Text>
                  </View>
                  <Text style={styles.arrowIcon}>›</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Sélecteur de langue (comme LanguageSwitcherTwo) */}
            <View style={styles.languageSection}>
              <Text style={styles.sectionTitle}>{t('language')}</Text>
              <View style={styles.languageList}>
                {supportedLanguages.map((language) => (
                  <TouchableOpacity
                    key={language}
                    style={[
                      styles.languageItem,
                      currentLanguage === language && styles.languageItemActive,
                    ]}
                    onPress={() => handleLanguageChange(language)}
                  >
                    <Text style={[
                      styles.languageText,
                      currentLanguage === language && styles.languageTextActive,
                    ]}>
                      {getLanguageName(language)}
                    </Text>
                    {currentLanguage === language && (
                      <Text style={styles.checkIcon}>✓</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Footer du menu */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              © 2024 E-Luxe. {t('copyright') || 'All rights reserved.'}
            </Text>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Overlay standard
  },
  container: {
    flex: 1,
    backgroundColor: ELuxeColors.white, // Fond blanc comme la charte E-Luxe
    marginLeft: 0,
    marginRight: '20%', // Menu prend 80% de la largeur
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20, // Augmenté de 16 à 20 pour accommoder le logo plus grand
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1, // Bordure E-Luxe
    backgroundColor: ELuxeColors.white, // Fond blanc
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logo: {
    width: 48, // Agrandi de 32 à 48 (50% plus grand)
    height: 48, // Agrandi de 32 à 48 (50% plus grand)
    marginRight: 16, // Augmenté pour équilibrer
  },
  logoPlaceholder: {
    width: 48, // Agrandi de 32 à 48 (50% plus grand)
    height: 48, // Agrandi de 32 à 48 (50% plus grand)
    borderRadius: 24, // Ajusté pour maintenir la forme circulaire
    backgroundColor: ELuxeColors.primary, // Couleur or E-Luxe
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16, // Augmenté pour équilibrer
  },
  logoText: {
    color: ELuxeColors.white,
    fontSize: 20, // Agrandi de 18 à 20 pour s'adapter au logo plus grand
    fontWeight: 'bold',
  },
  headerTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.primary, // Couleur or E-Luxe comme la charte
    fontWeight: 'bold',
    textTransform: 'uppercase', // Comme le client web
    flex: 1,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: ELuxeColors.grey2, // Fond gris clair E-Luxe
  },
  content: {
    flex: 1,
    paddingHorizontal: 0, // Pas de padding horizontal comme le web
  },
  menuList: {
    paddingVertical: 0, // Pas de padding vertical
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15, // Padding comme le client web
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1, // Bordure E-Luxe
    backgroundColor: 'transparent',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: 16,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIcon: {
    fontSize: 20,
    color: ELuxeColors.textPrimary, // Couleur texte E-Luxe
    fontWeight: 'bold',
  },
  arrowIcon: {
    fontSize: 18,
    color: ELuxeColors.textSecondary, // Couleur secondaire E-Luxe
  },
  checkIcon: {
    fontSize: 16,
    color: ELuxeColors.primary, // Couleur or E-Luxe
    fontWeight: 'bold',
  },
  menuText: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary, // Couleur texte E-Luxe (noir)
    flex: 1,
    fontSize: 16, // Taille comme le client web
    fontWeight: '600', // Plus gras pour meilleure visibilité
    textTransform: 'capitalize', // Comme le client web
    lineHeight: 20, // Interligne pour meilleure lisibilité
  },
  languageSection: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: ELuxeColors.border1, // Bordure E-Luxe
    backgroundColor: ELuxeColors.grey2, // Fond gris clair E-Luxe
  },
  sectionTitle: {
    color: ELuxeColors.textPrimary, // Couleur texte E-Luxe
    fontSize: 18, // Taille plus grande pour visibilité
    fontWeight: '700', // Très gras pour visibilité
    fontFamily: 'Roboto', // Police Android native
    marginBottom: 16,
    lineHeight: 22, // Interligne pour lisibilité
  },
  languageList: {
    gap: 8,
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: ELuxeColors.white, // Fond blanc
    borderWidth: 1,
    borderColor: ELuxeColors.border1, // Bordure E-Luxe
  },
  languageItemActive: {
    backgroundColor: ELuxeColors.primaryLight, // Couleur or E-Luxe transparente
    borderWidth: 1,
    borderColor: ELuxeColors.primary, // Couleur or E-Luxe
  },
  languageText: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary, // Couleur texte E-Luxe
  },
  languageTextActive: {
    color: ELuxeColors.primary, // Couleur or E-Luxe
    fontWeight: '600',
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: ELuxeColors.border1,
    backgroundColor: ELuxeColors.white, // Fond blanc
  },
  footerText: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
  },
});

export default MobileMenu;
