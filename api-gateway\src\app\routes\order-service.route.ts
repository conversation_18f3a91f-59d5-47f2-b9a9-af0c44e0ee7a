import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import customResponse from "../utils/custom-response.util";
import errorNumbers from "../utils/error-numbers.util";
import statusCode from "../utils/status-code.util";
import config from "../../config";
import { formatErrorMessages } from "../utils/helpers.util";

const orderServiceProxy = httpProxy(config.orderServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: formatErrorMessages(err.errors) || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-08-05
 *
 * Class OrderServiceRoutes
 */
class OrderServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-05
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all order service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-05
   *
   * @returns {Router} of order service
   */
  public orderServiceRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All order routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/orders",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/user/:userId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/recent", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/dashboard-recent-order", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/dashboard-amount", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/dashboard-count", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/best-seller/chart", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/order",
              routesGrouping.group((router) => {
                router.get("/:orderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.put("/:orderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.patch("/:orderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.delete("/:orderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All shipping zone routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/shippingZones",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/shippingZone",
              routesGrouping.group((router) => {
                router.get("/:shippingZoneId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.put("/:shippingZoneId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.delete("/:shippingZoneId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All shipping price routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/shippingPrices",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/shippingPrice",
              routesGrouping.group((router) => {
                router.get("/:shippingPriceId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.get(
                  "/departure/:departure/arrival/:arrival",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    orderServiceProxy(req, res, next);
                  }
                );

                router.put("/:shippingPriceId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.delete("/:shippingPriceId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All shipping method routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/shippingMethods",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });

                router.get("/:countryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  orderServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/shippingMethod",
              routesGrouping.group((router) => {
                router.get("/:shippingMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.put("/:shippingMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });

                router.delete("/:shippingMethodId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All order counter routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/orderCounter",
              routesGrouping.group((router) => {
                router.post("/init", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  orderServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const orderServiceRoutes = new OrderServiceRoutes();
export default orderServiceRoutes;
