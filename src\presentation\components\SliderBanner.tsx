/**
 * Composant SliderBanner - Mobile E-Luxe 1.0
 * Reproduction EXACTE du composant Slider1.tsx du client web
 * Utilise storeCustomizationSetting.slider depuis l'API
 */

import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Dimensions,
} from 'react-native';
import {useAppSelector} from '../store/store';
import {ELuxeColors} from '../../theme/colors';
import {TextStyles} from '../../theme/typography';

const {width} = Dimensions.get('window');

interface SlideItemProps {
  title: any; // Objet multilingue {fr, en}
  description: any; // Objet multilingue {fr, en}
  img: string;
  buttonText: any; // Objet multilingue {fr, en}
  onPress?: () => void;
}

/**
 * SlideItem - Reproduction exacte du SlideItem du client web
 */
const SlideItem: React.FC<SlideItemProps> = ({
  title,
  description,
  img,
  buttonText,
  onPress,
}) => {
  // Fonction showingTranslateValue EXACTE du client web
  const showingTranslateValue = (value: any): string => {
    if (typeof value === 'string') return value;
    if (!value) return '';
    if (typeof value === 'object') {
      // Priorité: en > fr > de > première valeur disponible (comme le client web)
      return value.en || value.fr || value.de || Object.values(value)[0] || '';
    }
    return String(value);
  };

  // Traitement de la description EXACTEMENT comme le client web
  const descriptionParts = showingTranslateValue(description)?.split(',') || [];
  const titleText = showingTranslateValue(title);
  const buttonTextValue = showingTranslateValue(buttonText);

  return (
    <ImageBackground
      source={{
        uri: img || 'https://via.placeholder.com/600x400/D4AF37/FFFFFF?text=E-Luxe+Slide',
      }}
      style={styles.slideItem}
      resizeMode="cover"
      onError={() => {
        console.log('⚠️ Erreur chargement image slide:', img);
      }}
    >
      <View style={styles.slideContent}>
        {/* tp-slide-item__sub-title - EXACTEMENT comme le client web */}
        <Text style={styles.slideSubTitle}>{titleText}</Text>

        {/* Description (comme tp-slide-item__title) */}
        <View style={styles.slideDescriptionContainer}>
          <Text style={styles.slideTitle}>
            {descriptionParts[0]}
            {descriptionParts[1] && (
              <Text style={styles.slideHighlight}>{descriptionParts[1]}</Text>
            )}
            {descriptionParts[2]}
          </Text>
        </View>

        {/* Bouton (comme tp-slide-item__slide-btn) */}
        <TouchableOpacity style={styles.slideButton} onPress={onPress}>
          <Text style={styles.slideButtonText}>{buttonTextValue}</Text>
          <Text style={styles.buttonArrow}>›</Text>
        </TouchableOpacity>
      </View>
    </ImageBackground>
  );
};

interface SliderBannerProps {
  onSlidePress?: (slideIndex: number) => void;
}

/**
 * SliderBanner principal - Reproduction exacte de Slider1.tsx du client web
 */
const SliderBanner: React.FC<SliderBannerProps> = ({onSlidePress}) => {
  const {storeCustomizationSetting, isLoading} = useAppSelector(
    (state: any) => state.setting,
  );
  const slider = storeCustomizationSetting?.slider;

  // Debug pour voir les données reçues
  console.log('🎠 SliderBanner - storeCustomizationSetting:', storeCustomizationSetting);
  console.log('🎠 SliderBanner - slider:', slider);
  console.log('🎠 SliderBanner - isLoading:', isLoading);

  const [currentSlide, setCurrentSlide] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => {
        const nextSlide = (prev + 1) % 3;
        scrollViewRef.current?.scrollTo({
          x: nextSlide * width,
          animated: true,
        });
        return nextSlide;
      });
    }, 2500);

    return () => clearInterval(interval);
  }, [slider]);

  // Gestion du scroll manuel
  const handleScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentSlide(slideIndex);
  };

  const handleSlidePress = (slideIndex: number) => {
    onSlidePress?.(slideIndex);
  };

  // Si pas de données slider API, ne rien afficher - EXACTEMENT comme le client web
  if (!slider) {
    console.log('⚠️ SliderBanner: Pas de données slider depuis l\'API, composant masqué');
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Slider principal (comme swiper-container slider-active) */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}>
        {/* Premier slide */}
        <SlideItem
          title={slider.first_title}
          description={slider.first_description}
          img={slider.first_img}
          buttonText={slider.first_button}
          onPress={() => {
            handleSlidePress(0);
          }}
        />

        {/* Deuxième slide */}
        <SlideItem
          title={slider.second_title}
          description={slider.second_description}
          img={slider.second_img}
          buttonText={slider.second_button}
          onPress={() => handleSlidePress(1)}
        />

        {/* Troisième slide */}
        <SlideItem
          title={slider.third_title}
          description={slider.third_description}
          img={slider.third_img}
          buttonText={slider.third_button}
          onPress={() => handleSlidePress(2)}
        />
      </ScrollView>

      {/* Pagination (comme slider-pagination) */}
      <View style={styles.pagination}>
        {[0, 1, 2].map(index => (
          <TouchableOpacity
            key={index}
            style={[
              styles.paginationDot,
              currentSlide === index && styles.paginationDotActive,
            ]}
            onPress={() => {
              setCurrentSlide(index);
              scrollViewRef.current?.scrollTo({
                x: index * width,
                animated: true,
              });
            }}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // COULEURS EXACTES DU CLIENT WEB E-LUXE :
  // - Sous-titre : #D4AF37 (--tp-text-primary)
  // - Titre : #040404 (--tp-text-body)
  // - Surbrillance : #FFCD00 (--tp-common-yellow)
  // - Bouton : #D4AF37 avec texte blanc
  container: {
    height: 300,
    backgroundColor: ELuxeColors.grey2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ELuxeColors.grey2,
  },
  loadingText: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
  },
  scrollView: {
    flex: 1,
  },
  slideItem: {
    width: width,
    height: 300,
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
  },

  slideContent: {
    flex: 1,
    justifyContent: 'center',
    zIndex: 2, // Au-dessus de l'overlay
    paddingVertical: 40,
  },
  slideSubTitle: {
    color: ELuxeColors.primary, // #D4AF37 - Couleur or exacte du client web
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Roboto',
    marginBottom: 8,
    textTransform: 'uppercase',
    letterSpacing: 1,
    lineHeight: 18,
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
  },
  slideDescriptionContainer: {
    marginBottom: 20,
  },
  slideTitle: {
    color: ELuxeColors.textPrimary, // #040404 - Couleur texte exacte du client web
    fontSize: 28,
    fontWeight: '700',
    fontFamily: 'Roboto',
    lineHeight: 36,
    marginBottom: 16,
    textShadowColor: 'rgba(255, 255, 255, 0.9)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 3,
  },
  slideHighlight: {
    color: ELuxeColors.yellow, // #FFCD00 - Couleur jaune exacte du client web
    fontStyle: 'italic',
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
  },
  slideButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primary, // #D4AF37 - Couleur or exacte du client web
    paddingHorizontal: 24,
    paddingVertical: 16, // Plus de padding pour meilleure visibilité
    borderRadius: 8,
    alignSelf: 'flex-start',
    gap: 8,
    shadowColor: ELuxeColors.black,
    shadowOffset: {width: 0, height: 4}, // Ombre plus forte
    shadowOpacity: 0.4, // Ombre plus visible
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 2, // Bordure pour meilleur contraste
    borderColor: ELuxeColors.white,
  },
  slideButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.black, // Noir sur fond or pour contraste maximal
    fontWeight: '700', // Plus gras
    fontSize: 16, // Plus grand
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
  },
  buttonArrow: {
    fontSize: 18, // Plus grand
    color: ELuxeColors.black, // Noir sur fond or pour contraste maximal
    fontWeight: 'bold',
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
  },
  // Styles d'image supprimés car on utilise maintenant ImageBackground
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: ELuxeColors.grey3,
  },
  paginationDotActive: {
    backgroundColor: ELuxeColors.primary,
    width: 24,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
  },
});

export default SliderBanner;
