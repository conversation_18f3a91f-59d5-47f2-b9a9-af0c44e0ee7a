/**
 * DealsScreen - Mobile E-Luxe 1.0
 * Page des offres spéciales reproduisant le contenu du client web
 */

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { getDealsAsync } from '../../store/slices/productSlice';
import ProductCard from '../../components/ProductCard';
import { ELuxeColors, ComponentColors } from '../../../theme/colors';
import { TextStyles } from '../../../theme/typography';
import Icon from 'react-native-vector-icons/MaterialIcons';

const DealsScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  
  const { products, isLoading } = useAppSelector((state) => state.product);

  useEffect(() => {
    loadDeals();
  }, []);

  const loadDeals = async () => {
    try {
      await dispatch(getDealsAsync()).unwrap();
    } catch (error) {
      console.error('Erreur lors du chargement des offres:', error);
    }
  };

  const handleProductPress = (product: any) => {
    navigation.navigate('ProductDetail', { productId: product.id || product._id });
  };

  const dealCategories = [
    { id: 'flash', title: 'Flash Sales', icon: 'flash-on', color: '#FF6B6B' },
    { id: 'daily', title: 'Daily Deals', icon: 'today', color: '#4ECDC4' },
    { id: 'weekly', title: 'Weekly Offers', icon: 'date-range', color: '#45B7D1' },
    { id: 'clearance', title: 'Clearance', icon: 'local-offer', color: '#96CEB4' },
  ];

  const renderDealCategory = ({ item }: { item: any }) => (
    <TouchableOpacity style={[styles.categoryCard, { borderColor: item.color }]}>
      <View style={[styles.categoryIcon, { backgroundColor: item.color }]}>
        <Icon name={item.icon} size={24} color={ELuxeColors.white} />
      </View>
      <Text style={styles.categoryTitle}>{item.title}</Text>
      <Text style={styles.categorySubtitle}>Special offers</Text>
    </TouchableOpacity>
  );

  const renderProductItem = ({ item }: { item: any }) => (
    <View style={styles.productItem}>
      <ProductCard
        product={item}
        onPress={() => handleProductPress(item)}
        style={styles.productCard}
      />
      {item.discount && (
        <View style={styles.discountBadge}>
          <Text style={styles.discountText}>-{item.discount}%</Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Special Deals</Text>
          <Text style={styles.headerSubtitle}>
            Discover amazing offers on luxury products
          </Text>
        </View>

        {/* Deal Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Deal Categories</Text>
          <FlatList
            data={dealCategories}
            renderItem={renderDealCategory}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          />
        </View>

        {/* Featured Deals */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Deals</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={products.slice(0, 6)}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id || item._id}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={styles.productsGrid}
          />
        </View>

        {/* Limited Time Offers */}
        <View style={styles.limitedTimeSection}>
          <View style={styles.limitedTimeHeader}>
            <Icon name="access-time" size={24} color={ELuxeColors.white} />
            <Text style={styles.limitedTimeTitle}>Limited Time Offers</Text>
          </View>
          <Text style={styles.limitedTimeSubtitle}>
            Hurry up! These deals won't last long
          </Text>
          
          <FlatList
            data={products.slice(0, 4)}
            renderItem={renderProductItem}
            keyExtractor={(item) => `limited-${item.id || item._id}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.limitedTimeProducts}
          />
        </View>

        {/* Newsletter Signup */}
        <View style={styles.newsletterSection}>
          <Text style={styles.newsletterTitle}>Never Miss a Deal</Text>
          <Text style={styles.newsletterDescription}>
            Subscribe to our newsletter and be the first to know about exclusive offers
          </Text>
          <TouchableOpacity style={styles.subscribeButton}>
            <Text style={styles.subscribeButtonText}>Subscribe Now</Text>
            <Icon name="email" size={20} color={ELuxeColors.white} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ComponentColors.screen.background,
  },
  header: {
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 20,
    paddingVertical: 32,
    alignItems: 'center',
  },
  headerTitle: {
    ...TextStyles.h1,
    color: ELuxeColors.white,
    marginBottom: 8,
  },
  headerSubtitle: {
    ...TextStyles.body,
    color: ELuxeColors.white,
    textAlign: 'center',
    opacity: 0.9,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textPrimary,
  },
  viewAllText: {
    ...TextStyles.body,
    color: ELuxeColors.primary,
    fontWeight: '600',
  },
  categoriesContainer: {
    paddingRight: 20,
  },
  categoryCard: {
    backgroundColor: ELuxeColors.white,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginRight: 12,
    width: 120,
    borderWidth: 2,
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    ...TextStyles.h5,
    color: ELuxeColors.textPrimary,
    textAlign: 'center',
    marginBottom: 4,
  },
  categorySubtitle: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
  },
  productsGrid: {
    gap: 12,
  },
  productItem: {
    flex: 1,
    marginHorizontal: 6,
    marginBottom: 12,
    position: 'relative',
  },
  productCard: {
    // Style par défaut
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    zIndex: 1,
  },
  discountText: {
    ...TextStyles.caption,
    color: ELuxeColors.white,
    fontWeight: 'bold',
  },
  limitedTimeSection: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  limitedTimeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  limitedTimeTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.white,
  },
  limitedTimeSubtitle: {
    ...TextStyles.body,
    color: ELuxeColors.white,
    opacity: 0.9,
    marginBottom: 16,
  },
  limitedTimeProducts: {
    paddingRight: 20,
  },
  newsletterSection: {
    backgroundColor: ELuxeColors.white,
    paddingHorizontal: 20,
    paddingVertical: 32,
    alignItems: 'center',
  },
  newsletterTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  newsletterDescription: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  subscribeButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
    fontWeight: '600',
  },
});

export default DealsScreen;
