{"login": {"passwordNotFound": "Le mot de passe ne peut pas être vide.", "userNameNotFound": "Le nom d'utilisateur ne peut pas être vide.", "userLastNameNotFound": "Le nom de famille de l'utilisateur ne peut pas être vide.", "userIdNotFound": "L'identifiant de l'utilisateur ne peut pas être vide.", "emailNotFound": "Le courrier électronique ne peut pas être vide.", "genderNotFound": "Le genre ne peut pas être vide.", "userNameDetailFailed": "Échec de la saisie des données de l'utilisateur", "userRegistrationOk": "L'enregistrement de l'utilisateur a été effectué avec succès.", "userRegistrationFailed": "L'enregistrement de l'utilisateur n'a pas abouti.", "userLoginOk": "Utilisateur connecté.", "userLoginFailed": "Email ou mot de passe incorrect."}, "unauthorize": {"noAccessToken": "Pas de jeton d'accès", "invalidAccessToken": "Jeton d'accès invalide", "invalidAdminToken": "<PERSON><PERSON> d'administration invalide", "invalidSellerToken": "Jeton de vendeur invalide", "invalidAdminOrSeller": "Jeton d'administrateur ou de vendeur invalide", "invalidRefreshToken": "Jeton de rafraîchissement invalide", "noRefreshToken": "Pas de jeton de rafraîchissement"}, "profile": {"userNotFound": "L'utilisateur ne sort pas."}, "others": {"routeNotFound": "Itinéraire non trouvé"}, "cache": {"redis": {"redisConnectionTo": "La connexion Redis à", "failed": "a échoué"}}}