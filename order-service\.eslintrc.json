{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"max-len": ["error", {"code": 120, "tabWidth": 4, "comments": 120}], "no-trailing-spaces": ["error", {"skipBlankLines": false}], "no-multiple-empty-lines": ["error", {"max": 1, "maxEOF": 0}], "eol-last": ["error", "always"], "valid-jsdoc": "error", "@typescript-eslint/no-explicit-any": ["off"], "require-jsdoc": ["error", {"require": {"FunctionDeclaration": true, "MethodDefinition": true, "ClassDeclaration": true, "ArrowFunctionExpression": true, "FunctionExpression": false}}]}}