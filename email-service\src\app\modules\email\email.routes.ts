import express, { Router } from "express";
import dotenv from "dotenv";
import routesGrouping from "../../utils/routes-grouping.util";
import emailController from "./email.controller";

dotenv.config();

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-22
 *
 * Class EmailRoutes
 */
class EmailRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-04-21
   */
  constructor() {
    this.router = express.Router({ mergeParams: true });
  }

  /**
   * Creating all emails routes
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-04-21
   *
   * @returns {Router} the email routes
   */
  public emailRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        router.use(
          "/email",
          routesGrouping.group((router) => {
            /**
             * @swagger
             * /v1/{lang}/email/send:
             *   post:
             *     tags:
             *     - Email
             *     operationId: send
             *     summary: Send an email.
             *     description: Send an email.
             *     parameters:
             *      - in: path
             *        name: lang
             *        schema:
             *          type: string
             *          example: en
             *        required: true
             *        description: Language for the response. Supported
             *          languages ['en', 'fr']
             *
             *     requestBody:
             *       required: true
             *       content:
             *         application/x-www-form-urlencoded:
             *           schema:
             *             type: object
             *             properties:
             *               senderName:
             *                 type: string
             *                 description: The sender name.
             *                 example: Kitecole
             *               senderEmail:
             *                 type: string
             *                 description: The sender address.
             *                 example: <EMAIL>
             *                 format: email
             *               receivers:
             *                 type: string
             *                 description: The list of receivers.
             *                 example: <EMAIL>, <EMAIL>
             *                 required: true
             *               subject:
             *                 type: string
             *                 description: Subject line.
             *                 example: Hello world?
             *               body:
             *                 description:  html body
             *                 type: string
             *                 example: <b>Hello world?</b>
             *
             *         application/json:
             *           schema:
             *             type: object
             *             properties:
             *               senderName:
             *                 type: string
             *                 description: The sender name.
             *                 example: Kitecole
             *               senderEmail:
             *                 type: string
             *                 description: The sender address.
             *                 example: <EMAIL>
             *                 format: email
             *               receivers:
             *                 type: string
             *                 description: The list of receivers.
             *                 example: <EMAIL>, <EMAIL>
             *                 required: true
             *               subject:
             *                 type: string
             *                 description: Subject line.
             *                 example: Hello world?
             *               body:
             *                 description:  html body
             *                 type: string
             *                 example: <b>Hello world?</b>
             *
             *     responses:
             *       200:
             *         description: Email sent successfully.
             *         content:
             *           application/json:
             *             schema:
             *                type: object
             *                properties:
             *                  status:
             *                    type: string
             *                    example: Ok
             *                  data:
             *                    $ref: '#/components/schemas/Email'
             *
             *       400:
             *         description: Bad Request.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/400'
             *
             *       412:
             *         description: Precondition Failed.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/412'
             *       500:
             *         description: Internal Server Error.
             *         content:
             *          application/json:
             *             schema:
             *              $ref: '#/responses/schemas/500'
             *
             */
            router.post("/send", emailController.sendEmail);
          })
        );
      })
    );
  }
}

const emailRoutes = new EmailRoutes();
export default emailRoutes;
