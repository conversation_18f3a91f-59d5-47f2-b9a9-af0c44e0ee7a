import mongoose from "mongoose";

const reviewSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true },
    comment: { type: String, required: true },
    rating: { type: Number, required: true },
  },
  {
    timestamps: {
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
  }
);

const postSchema = new mongoose.Schema(
  {
    name: { type: Object, required: false },
    title: { type: Object, required: true },
    slug: { type: String, required: true },
    image: { type: Array, required: false },
    short_description: { type: String, required: false },
    description: { type: Object, required: false },
    rating: { type: Number, required: false },
    num_reviews: { type: Number, required: false },
    reviews: [reviewSchema],
    tags: [{ type: mongoose.Schema.Types.ObjectId, ref: "tag" }],
    tag: [String],
    categories: [{ type: mongoose.Schema.Types.ObjectId, ref: "category" }],
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "category",
      required: true,
    },
    status: { type: String, default: "show", enum: ["show", "hide"] },
    related_posts: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "post",
      },
    ],
    publish_date: { type: Date, required: false },
    user_id: { type: mongoose.Schema.Types.ObjectId, required: false },
    user_name: { type: String, required: false },
  },
  {
    timestamps: {
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
  }
);

const Post = mongoose.model("post", postSchema);

export default Post;
