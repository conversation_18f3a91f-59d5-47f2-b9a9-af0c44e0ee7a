import express, { NextFunction, Request, Response, Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import jwtUtilities from "../utils/jwt-utilities.util";
import statusCode from "../utils/status-code.util";
import errorNumbers from "../utils/error-numbers.util";
import customResponse from "../utils/custom-response.util";
import config from "../../config";
import { formatErrorMessages, isMultipartRequest } from "../utils/helpers.util";

const userServiceProxy = function (
  req: Request,
  res: Response,
  next: NextFunction
) {
  return (function (req: Request, res: Response, next: NextFunction) {
    let reqBodyEncoding;
    let reqAsBuffer = false;
    let parseReqBody = true;
    if (isMultipartRequest(req)) {
      reqAsBuffer = true;
      reqBodyEncoding = null;
      parseReqBody = false;
    }
    return httpProxy(config.userServiceUrl, {
      reqAsBuffer,
      reqBodyEncoding,
      parseReqBody,
      proxyErrorHandler: function (err, res, next) {
        switch (err && err.code) {
          case "ECONNRESET": {
            const response = {
              status: err?.status || statusCode.httpInternalServerError,
              errNo: errorNumbers.genericError,
              errMsg: err?.message || JSON.stringify(err),
            };

            return customResponse.error(response, res);
          }
          case "ECONNREFUSED": {
            const response = {
              status: err?.status || statusCode.httpInternalServerError,
              errNo: errorNumbers.genericError,
              errMsg: formatErrorMessages(err.errors) || JSON.stringify(err),
            };

            return customResponse.error(response, res);
          }
          default: {
            next(err);
          }
        }
      },
    })(req, res, next);
  })(req, res, next);
};

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-26-03
 *
 * Class UserServiceRoutes
 */
class UserServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-03-26
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all user service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-26-03
   *
   * @returns {Router} of user service
   */
  public userServiceRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All gender routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/genders",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/gender",
              routesGrouping.group((router) => {
                router.get("/:genderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.put("/:genderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.delete("/:genderId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All role routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/roles",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/role",
              routesGrouping.group((router) => {
                router.get("/:roleId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.put("/:roleId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.delete("/:roleId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All user routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/users",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
                router.post("/register", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/customers", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/staff", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.post("/subscribe", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.post(
                  "/login",
                  (req: Request, res: Response, next: NextFunction) => {
                    const bearerToken = jwtUtilities.generateInternToken();

                    // Set request header authorization with generic gateway
                    req.headers.authorization = `Bearer ${bearerToken}`;
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;

                    userServiceProxy(req, res, next);
                  }
                );

                router.get("/logout", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.get("/images", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/user",
              routesGrouping.group((router) => {
                router.get("/:userId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/username/:userName", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.put("/:userId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.patch("/:userId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.post("/:userId/resetPassword", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.get("/:userId/role/:roleId/assign", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.get(
                  "/:userId/role/:roleId/unassign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    userServiceProxy(req, res, next);
                  }
                );

                router.post("/sendResetPasswordLink", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.delete("/:userId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All subscribers routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/subscribers",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
                router.post("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/subscriber",
              routesGrouping.group((router) => {
                router.get("/:subscriberId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.put("/:subscriberId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.patch("/:subscriberId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.delete("/:subscriberId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All countries routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/countries",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/country",
              routesGrouping.group((router) => {
                router.get("/:countryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.put("/:countryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.delete("/:countryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All states routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/states",
              routesGrouping.group((router) => {
                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });

                router.get("/country/:countryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  userServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/state",
              routesGrouping.group((router) => {
                router.get("/:stateId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.put("/:stateId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });

                router.delete("/:stateId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }

  /**
   * Creating all user service images routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-26-03
   *
   * @returns {Router} of user service
   */
  public userServiceImageRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All user routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/users",
              routesGrouping.group((router) => {
                router.use("/images", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  console.log(req.url);
                  userServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const userServiceRoutes = new UserServiceRoutes();
export default userServiceRoutes;
