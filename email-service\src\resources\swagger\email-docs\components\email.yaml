components:
  schemas:
    Email:
      type: object
      properties:
        _id:
          type: string
        message_id:
          type: string
        sender_name: 
          type: string
        subject:
          type: string
        body:
          type: string
        accepted:
          type: array
          items:
        rejected:
          type: array
          items:
        envelope_time:
          type: number
        message_time:
          type: number
        message_size:
          type: number
        response:
          type: string
        envelope:
          type: object
          properties:
            from:
              type: string
            to:
              type: array
              items:
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time