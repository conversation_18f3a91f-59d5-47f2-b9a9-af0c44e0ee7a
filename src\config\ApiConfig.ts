/**
 * Configuration API pour Mobile E-Luxe 1.0
 * Compatible avec l'architecture microservices existante
 * Synchronisé avec client-e-luxe et utilise les mêmes endpoints
 */

// Variables d'environnement pour React Native
declare var process: {
  env: {
    NODE_ENV: string;
    [key: string]: string | undefined;
  };
};

export interface ApiEnvironment {
  name: string;
  apiGatewayUrl: string;
  webAppUrl: string;
  socketUrl?: string;
  microservices: {
    authentication: string;
    blog: string;
    email: string;
    order: string;
    payment: string;
    product: string;
    user: string;
    setting: string;
  };
}

// Configuration API SIMPLE et COHÉRENTE
const getApiUrl = (): string => {
  if (__DEV__) {
    // EN DÉVELOPPEMENT : Utiliser l'API Gateway local sur port 2000
    return 'http://localhost:2000';
  } else {
    // EN PRODUCTION : Utiliser l'API de production
    return 'https://api.e-luxe.fr';
  }
};

// Configuration pour différents environnements (synchronisée avec client-e-luxe)
// Communication directe avec le vrai backend E-Luxe
export const API_ENVIRONMENTS: Record<string, ApiEnvironment> = {
  // Configuration locale pour test (si l'API principale ne fonctionne pas)
  local: {
    name: 'Local Test',
    apiGatewayUrl: 'http://localhost:2000', // Essayer localhost direct
    webAppUrl: 'http://localhost:3000',
    socketUrl: 'http://localhost:2000',
    microservices: {
      authentication: 'http://localhost:2000',
      blog: 'http://localhost:2000',
      email: 'http://localhost:2000',
      order: 'http://localhost:2000',
      payment: 'http://localhost:2000',
      product: 'http://localhost:2000',
      user: 'http://localhost:2000',
      setting: 'http://localhost:2000',
    },
  },
  development: {
    name: 'Development',
    // Essayer différentes adresses selon l'environnement
    apiGatewayUrl: getApiUrl(), // API Gateway base URL
    webAppUrl: 'http://localhost:3000', // Client web E-Luxe
    socketUrl: getApiUrl(), // WebSocket sur même port que gateway
    microservices: {
      authentication: getApiUrl(), // API Gateway (tout passe par le gateway)
      blog: getApiUrl(), // Via API Gateway
      email: getApiUrl(), // Via API Gateway
      order: getApiUrl(), // Via API Gateway
      payment: getApiUrl(), // Via API Gateway
      product: getApiUrl(), // Via API Gateway
      user: getApiUrl(), // Via API Gateway
      setting: getApiUrl(), // Via API Gateway
    },
  },
  staging: {
    name: 'Staging',
    apiGatewayUrl: 'https://api-staging.e-luxe.fr/api',
    webAppUrl: 'https://staging.e-luxe.fr',
    socketUrl: 'https://api-staging.e-luxe.fr',
    microservices: {
      authentication: 'https://auth-staging.e-luxe.fr',
      blog: 'https://blog-staging.e-luxe.fr',
      email: 'https://email-staging.e-luxe.fr',
      order: 'https://order-staging.e-luxe.fr',
      payment: 'https://payment-staging.e-luxe.fr',
      product: 'https://product-staging.e-luxe.fr',
      user: 'https://user-staging.e-luxe.fr',
      setting: 'https://setting-staging.e-luxe.fr',
    },
  },
  production: {
    name: 'Production',
    apiGatewayUrl: 'https://api.e-luxe.fr/api',
    webAppUrl: 'https://client.e-luxe.fr',
    socketUrl: 'https://api.e-luxe.fr',
    microservices: {
      authentication: 'https://api.e-luxe.fr',
      blog: 'https://blog-service.e-luxe.fr',
      email: 'https://email-service.e-luxe.fr',
      order: 'https://order-service.e-luxe.fr',
      payment: 'https://payment-service.e-luxe.fr',
      product: 'https://product-service.e-luxe.fr',
      user: 'https://user-service.e-luxe.fr',
      setting: 'https://setting-service.e-luxe.fr',
    },
  },
};

// Détection automatique de l'environnement
export const getCurrentEnvironment = (): string => {
  // En développement, essayer différentes configurations
  if (__DEV__) {
    // Vous pouvez changer cette valeur pour tester différentes configurations :
    // 'local' - pour http://localhost:2000
    // 'development' - pour http://********:2000 (émulateur Android)
    return 'development'; // Changez en 'local' si l'émulateur ne fonctionne pas
  }

  // Pour la production, utiliser l'API de production
  return 'production';
};

// Configuration API actuelle
export const getApiConfig = (): ApiEnvironment => {
  const env = getCurrentEnvironment();
  const config = API_ENVIRONMENTS[env];

  // Log de debug pour diagnostiquer les problèmes de connexion
  if (__DEV__) {
    console.log('🔧 Configuration API:', {
      environment: env,
      apiGatewayUrl: config.apiGatewayUrl,
      webAppUrl: config.webAppUrl,
    });

    // Test de connectivité simple
    console.log('🌐 Test de connectivité vers:', config.apiGatewayUrl);

    // Affichage de l'URL complète pour debug
    const testUrl = `${config.apiGatewayUrl}/v1/en/products`;
    console.log('🔗 URL de test complète:', testUrl);
  }

  return config;
};

// Endpoints API avec chemin complet v1/{lang}
export const API_ENDPOINTS = {
  // Authentication - Chemin complet: http://localhost:2000/v1/en/auth/login
  AUTH: {
    LOGIN: '/v1/{lang}/auth/login', // POST http://localhost:2000/v1/en/auth/login
    REGISTER: '/v1/{lang}/auth/register',
    REFRESH: '/v1/{lang}/auth/refresh',
    LOGOUT: '/v1/{lang}/auth/logout',
    FORGOT_PASSWORD: '/v1/{lang}/auth/forgot-password',
    RESET_PASSWORD: '/v1/{lang}/auth/reset-password',
    CHANGE_PASSWORD: '/v1/{lang}/auth/change-password',
    PROFILE: '/v1/{lang}/auth/profile',
    VALIDATE: '/v1/{lang}/auth/validate',
    ME: '/v1/{lang}/auth/me',
  },
  
  // Products - Endpoints compatibles avec l'API Gateway E-Luxe
  PRODUCTS: {
    LIST: '/v1/{lang}/products', // GET /v1/en/products
    DETAIL: '/v1/{lang}/products/:id', // GET /v1/en/products/123 (corrigé)
    BY_ID: '/v1/{lang}/product/:id', // GET /v1/en/product/123 (alternatif)
    CATEGORIES: '/v1/{lang}/categories', // GET /v1/en/categories
    SEARCH: '/v1/{lang}/products/search', // GET /v1/en/products/search
    FEATURED: '/v1/{lang}/products/featured', // GET /v1/en/products/featured
    RELATED: '/v1/{lang}/product/:id/related', // GET /v1/en/product/123/related
    SHOWING: '/v1/{lang}/products/showing', // GET /v1/en/products/showing
  },

  // Orders
  ORDERS: {
    LIST: '/v1/{lang}/orders',
    CREATE: '/v1/{lang}/orders',
    DETAIL: '/v1/{lang}/orders/:id',
    CANCEL: '/v1/{lang}/orders/:id/cancel',
    TRACK: '/v1/{lang}/orders/:id/track',
  },

  // Cart
  CART: {
    GET: '/v1/{lang}/cart',
    ADD: '/v1/{lang}/cart/add',
    UPDATE: '/v1/{lang}/cart/update',
    REMOVE: '/v1/{lang}/cart/remove',
    CLEAR: '/v1/{lang}/cart/clear',
  },
  
  // User
  USER: {
    PROFILE: '/v1/{lang}/user/profile',
    UPDATE: '/v1/{lang}/user/profile',
    AVATAR: '/v1/{lang}/user/avatar',
    ADDRESSES: '/v1/{lang}/user/addresses',
    WISHLIST: '/v1/{lang}/user/wishlist',
  },

  // Blog
  BLOG: {
    POSTS: '/v1/{lang}/blog/posts',
    POST_DETAIL: '/v1/{lang}/blog/posts/:id',
    CATEGORIES: '/v1/{lang}/blog/categories',
  },

  // Settings - Endpoints API Gateway confirmés (AVEC /v1)
  SETTINGS: {
    GLOBAL: '/v1/{lang}/setting/global',
    STORE_CUSTOMIZATION: '/v1/{lang}/setting/store/customization',
    STORE_SETTING: '/v1/{lang}/setting/store-setting',
    APP: '/v1/{lang}/settings/app',
    NOTIFICATIONS: '/v1/{lang}/settings/notifications',
  },
};

// Configuration des timeouts
export const API_TIMEOUTS = {
  DEFAULT: 30000, // 30 secondes
  UPLOAD: 60000,  // 60 secondes pour les uploads
  DOWNLOAD: 120000, // 2 minutes pour les téléchargements
};

// Headers par défaut
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-Client-Type': 'mobile',
  'X-Client-Version': '1.0.0',
};

// Configuration des langues supportées (synchronisée avec i18n)
export const SUPPORTED_LANGUAGES = ['en', 'fr'];
export const DEFAULT_LANGUAGE = 'en';

// Fonction pour obtenir la langue actuelle depuis i18n
export const getCurrentLanguageFromI18n = (): string => {
  try {
    // Import dynamique pour éviter les dépendances circulaires
    const i18n = require('../i18n/i18n').default;
    return i18n.language || DEFAULT_LANGUAGE;
  } catch {
    return DEFAULT_LANGUAGE;
  }
};

// Utilitaires pour construire les URLs avec langue et paramètres
export const buildUrl = (endpoint: string, params?: Record<string, string>, lang?: string): string => {
  let url = endpoint;

  // Remplacer {lang} par la langue (utilise i18n ou défaut)
  const language = lang || getCurrentLanguageFromI18n();
  url = url.replace('{lang}', language);

  // Remplacer les autres paramètres (:id, etc.)
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`:${key}`, value);
    });
  }

  return url;
};

// Fonction helper pour construire l'URL complète avec base URL
export const buildFullUrl = (endpoint: string, params?: Record<string, string>, lang?: string): string => {
  const apiConfig = getApiConfig();
  const relativePath = buildUrl(endpoint, params, lang);
  return `${apiConfig.apiGatewayUrl}${relativePath}`;
};

// Configuration pour le retry des requêtes
export const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 seconde
  retryCondition: (error: any) => {
    return error.code === 'NETWORK_ERROR' || 
           (error.response && error.response.status >= 500);
  },
};

export default {
  environments: API_ENVIRONMENTS,
  getCurrentEnvironment,
  getApiConfig,
  endpoints: API_ENDPOINTS,
  timeouts: API_TIMEOUTS,
  headers: DEFAULT_HEADERS,
  languages: SUPPORTED_LANGUAGES,
  defaultLanguage: DEFAULT_LANGUAGE,
  buildUrl,
  retryConfig: RETRY_CONFIG,
};
