import node<PERSON><PERSON>er<PERSON>anager from "../../../core/mail";
import Email from "./email.model";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-22
 *
 * Class EmailService
 */
class EmailService {
  /**
   * Send mail
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-23
   *
   * @param {any} data the email parametters
   *
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async sendEmail(data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          nodeMailerManager
            .sendMail({
              senderName: data.senderName,
              senderEmail: data.senderEmail,
              receivers: data.receivers || "<EMAIL>",
              subject: data.subject,
              body: data.body,
            })
            .then((res: any) => {
              const email = new Email({
                message_id: res.messageId,
                sender_name: data.senderName,
                subject: data.subject,
                body: data.body,
                accepted: res.accepted,
                rejected: res.rejected,
                envelope_time: res.envelopeTime,
                message_time: res.messageTime,
                message_size: res.messageSize,
                response: res.response,
                envelope: res.envelope,
              });

              const createdEmail = email.save();

              resolve(createdEmail);
            })
            .catch((error) => reject(error));
        } catch (error) {
          reject(error);
        }
      })();
    });
  }
}

const emailService = new EmailService();
export default emailService;
