{"login": {"passwordNotFound": "Password can't be empty.", "userNameNotFound": "User name can't be empty", "userLastNameNotFound": "User last name can't be empty.", "userIdNotFound": "User id can't be empty.", "emailNotFound": "Email can't be empty.", "genderNotFound": "Gender can't be empty.", "userNameDetailFailed": "User details failed", "userRegistrationOk": "User registration successful.", "userRegistrationFailed": "User registration unsuccessful.", "userLoginOk": "User logged in.", "userLoginFailed": "Incorrect email or password."}, "unauthorize": {"noAccessToken": "No access token", "invalidAccessToken": "Invalid access token", "invalidAdminToken": "Invalid admin token", "invalidSellerToken": "Invalid seller token", "invalidAdminOrSeller": "Invalid admin or seller token", "invalidRefreshToken": "Invalid refresh token", "noRefreshToken": "No refresh token"}, "profile": {"userNotFound": "User does not exits."}, "others": {"routeNotFound": "Route not found"}, "cache": {"redis": {"redisConnectionTo": "Redis connection to", "failed": "failed"}}}