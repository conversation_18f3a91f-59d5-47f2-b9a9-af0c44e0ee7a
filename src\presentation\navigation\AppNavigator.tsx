import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../store/store';
import { initializeAuthAsync } from '../store/slices/authSlice';
import { ELuxeColors } from '../../theme/colors';

// Screens
import SplashScreen from '../screens/SplashScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import HomeScreen from '../screens/home/<USER>';
import ProductListScreen from '../screens/products/ProductListScreen';
import ProductDetailScreen from '../screens/products/ProductDetailScreen';
import CartScreen from '../screens/cart/CartScreen';
import CheckoutScreen from '../screens/checkout/CheckoutScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import OrdersScreen from '../screens/orders/OrdersScreen';
import OrderDetailScreen from '../screens/orders/OrderDetailScreen';
import WishlistScreen from '../screens/wishlist/WishlistScreen';
import SearchScreen from '../screens/search/SearchScreen';
import CategoriesScreen from '../screens/categories/CategoriesScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import BlogScreen from '../screens/blog/BlogScreen';
import BlogDetailScreen from '../screens/blog/BlogDetailScreen';
import FAQScreen from '../screens/faq/FAQScreen';
import ContactScreen from '../screens/contact/ContactScreen';
import ServicesScreen from '../screens/services/ServicesScreen';
import TrackOrderScreen from '../screens/track/TrackOrderScreen';
// Nouveaux écrans alignés avec le client web
import ShopScreen from '../screens/shop/ShopScreen';
import AboutScreen from '../screens/about/AboutScreen';
import DealsScreen from '../screens/deals/DealsScreen';
import CompareScreen from '../screens/compare/CompareScreen';

// Navigation types (ALIGNÉES avec le client web)
export type RootStackParamList = {
  Splash: undefined;
  Auth: undefined;
  Main: undefined;
  // Pages principales (comme le web)
  Shop: { categoryId?: string; filters?: any };
  ProductDetail: { productId: string };
  ProductList: { categoryId?: string; title?: string; filters?: any };
  Cart: undefined;
  Checkout: undefined;
  Wishlist: undefined;
  // Pages informatives (comme le web)
  About: undefined;
  Contact: undefined;
  FAQ: undefined;
  Services: undefined;
  // Fonctionnalités avancées (comme le web)
  Search: { query?: string };
  Deals: undefined;
  TrackOrder: undefined;
  OrderDetail: { orderId: string };
  // Blog (comme le web)
  Blog: undefined;
  BlogDetail: { postId: string };
  // Comparaison produits (comme le web)
  Compare: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Categories: undefined;
  Cart: undefined;
  Wishlist: undefined;
  Profile: undefined;
};

const RootStack = createNativeStackNavigator<RootStackParamList>();
const AuthStack = createNativeStackNavigator<AuthStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

// Auth Navigator
const AuthNavigator = () => {
  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Register" component={RegisterScreen} />
      <AuthStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </AuthStack.Navigator>
  );
};

// Main Tab Navigator
const MainNavigator = () => {
  const cartItems = useAppSelector((state) => state.cart.items);
  const cartItemCount = cartItems.reduce((total, item) => total + item.quantity, 0);

  return (
    <MainTab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Categories':
              iconName = 'category';
              break;
            case 'Cart':
              iconName = 'shopping-cart';
              break;
            case 'Wishlist':
              iconName = 'favorite';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'home';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: ELuxeColors.primary, // Or E-Luxe
        tabBarInactiveTintColor: ELuxeColors.textTertiary,
        tabBarStyle: {
          backgroundColor: ELuxeColors.white,
          borderTopWidth: 1,
          borderTopColor: ELuxeColors.border1,
          paddingBottom: 8,
          paddingTop: 8,
          height: 65,
          shadowColor: ELuxeColors.black,
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 8,
        },
        headerShown: false, // Masquer les headers par défaut pour un design plus propre
        headerStyle: {
          backgroundColor: ELuxeColors.white,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: ELuxeColors.border1,
        },
        headerTitleStyle: {
          fontSize: 18,
          fontWeight: '600',
          color: ELuxeColors.textPrimary,
        },
      })}
    >
      <MainTab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Home',
          tabBarLabel: 'Home',
        }}
      />
      <MainTab.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{
          title: 'Categories',
          tabBarLabel: 'Categories',
        }}
      />
      <MainTab.Screen
        name="Cart"
        component={CartScreen}
        options={{
          title: 'Cart',
          tabBarLabel: 'Cart',
          tabBarBadge: cartItemCount > 0 ? cartItemCount : undefined,
          tabBarBadgeStyle: {
            backgroundColor: ELuxeColors.error,
            color: ELuxeColors.white,
            fontSize: 12,
            fontWeight: 'bold',
          },
        }}
      />
      <MainTab.Screen
        name="Wishlist"
        component={WishlistScreen}
        options={{
          title: 'Wishlist',
          tabBarLabel: 'Wishlist',
        }}
      />
      <MainTab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profile',
          tabBarLabel: 'Profile',
        }}
      />
    </MainTab.Navigator>
  );
};

// Root Navigator
const AppNavigator = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isInitialized } = useAppSelector((state) => state.auth);

  useEffect(() => {
    dispatch(initializeAuthAsync());
  }, [dispatch]);

  if (!isInitialized) {
    return (
      <NavigationContainer>
        <RootStack.Navigator screenOptions={{ headerShown: false }}>
          <RootStack.Screen name="Splash" component={SplashScreen} />
        </RootStack.Navigator>
      </NavigationContainer>
    );
  }

  return (
    <NavigationContainer>
      <RootStack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <>
            <RootStack.Screen name="Main" component={MainNavigator} />
            <RootStack.Screen 
              name="ProductDetail" 
              component={ProductDetailScreen}
              options={{
                headerShown: true,
                title: 'Product Details',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen 
              name="Cart" 
              component={CartScreen}
              options={{
                headerShown: true,
                title: 'Shopping Cart',
                animation: 'slide_from_bottom',
              }}
            />
            <RootStack.Screen 
              name="Checkout" 
              component={CheckoutScreen}
              options={{
                headerShown: true,
                title: 'Checkout',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen 
              name="OrderDetail" 
              component={OrderDetailScreen}
              options={{
                headerShown: true,
                title: 'Order Details',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen 
              name="Search" 
              component={SearchScreen}
              options={{
                headerShown: true,
                title: 'Search',
                animation: 'slide_from_top',
              }}
            />
            <RootStack.Screen
              name="ProductList"
              component={ProductListScreen}
              options={{
                headerShown: true,
                title: 'Products',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="Blog"
              component={BlogScreen}
              options={{
                headerShown: true,
                title: 'Blog',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="BlogDetail"
              component={BlogDetailScreen}
              options={{
                headerShown: true,
                title: 'Article',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="FAQ"
              component={FAQScreen}
              options={{
                headerShown: true,
                title: 'FAQ',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="Contact"
              component={ContactScreen}
              options={{
                headerShown: true,
                title: 'Contact Us',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="Services"
              component={ServicesScreen}
              options={{
                headerShown: true,
                title: 'Our Services',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="TrackOrder"
              component={TrackOrderScreen}
              options={{
                headerShown: true,
                title: 'Track Order',
                animation: 'slide_from_right',
              }}
            />
            {/* Nouveaux écrans alignés avec le client web */}
            <RootStack.Screen
              name="Shop"
              component={ShopScreen}
              options={{
                headerShown: true,
                title: 'Shop',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="About"
              component={AboutScreen}
              options={{
                headerShown: true,
                title: 'About E-Luxe',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="Deals"
              component={DealsScreen}
              options={{
                headerShown: true,
                title: 'Deals & Offers',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="Compare"
              component={CompareScreen}
              options={{
                headerShown: true,
                title: 'Compare Products',
                animation: 'slide_from_right',
              }}
            />
            <RootStack.Screen
              name="Wishlist"
              component={WishlistScreen}
              options={{
                headerShown: true,
                title: 'My Wishlist',
                animation: 'slide_from_right',
              }}
            />
          </>
        ) : (
          <RootStack.Screen name="Auth" component={AuthNavigator} />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
