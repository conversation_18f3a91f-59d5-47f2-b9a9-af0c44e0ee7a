import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import jwtUtilities from "../utils/jwt-utilities.util";
import customResponse from "..//utils/custom-response.util";
import errorNumbers from "../utils/error-numbers.util";
import statusCode from "../utils/status-code.util";
import config from "../../config";
import { formatErrorMessages } from "../utils/helpers.util";

const productServiceProxy = httpProxy(config.productServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: formatErrorMessages(err.errors) || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-06-21
 *
 * Class ProductServiceRoutes
 */
class ProductServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-06-21
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all product service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-06-21
   *
   * @returns {Router} of product service
   */
  public productServiceRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All product routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/products",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/best-sellers", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/brands", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing/store", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.delete("/:productIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/product",
              routesGrouping.group((router) => {
                router.get("/:productId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.get("/slug/:slug", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.post("/:productId/reviews", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:productId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.patch("/:productId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.get(
                  "/:productId/category/:categoryId/assign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    productServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:productId/category/:categoryId/unassign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    productServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:productId/tag/:tagId/assign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    productServiceProxy(req, res, next);
                  }
                );

                router.get(
                  "/:productId/tag/:tagId/unassign",
                  (req, res, next) => {
                    // Update url with original url which contain all path
                    req.url = req.originalUrl;
                    productServiceProxy(req, res, next);
                  }
                );

                router.delete("/:productId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All category routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/categories",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/featured", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showingProductsOnHomepage", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/withProducts", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/all", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:categoryIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/category",
              routesGrouping.group((router) => {
                router.get("/:categoryId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.get("/slug/:slug", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:categoryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.patch("/:categoryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:categoryId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:categoryId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All coupons routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/coupons",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:couponIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/coupon",
              routesGrouping.group((router) => {
                router.get("/:couponId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.get("/code/:couponCode", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:couponId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:couponId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:couponId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All attributes routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/attributes",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing/test", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/children", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:attributeIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/attribute",
              routesGrouping.group((router) => {
                router.get("/:attributeId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.get("/:attributeId/child/:childId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:attributeId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:attributeId/children", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:attributeId/child", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:attributeId/child/:childId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:attributeId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:attributeId/children/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:attributeId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:attributeId/child/:childId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:attributeId/children/:childIds", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All tag routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/tags",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.delete("/:tagIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/tag",
              routesGrouping.group((router) => {
                router.get("/:tagId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:tagId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:tagId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:tagId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );
          })
        );

        // All extras routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/extras",
              routesGrouping.group((router) => {
                router.get("/", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.get("/showing", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.post("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  productServiceProxy(req, res, next);
                });

                router.put("/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:extraIds/many", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );

            router.use(
              "/extra",
              routesGrouping.group((router) => {
                router.get("/:extraId", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;

                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:extraId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.put("/:extraId/status", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });

                router.delete("/:extraId", (req, res, next) => {
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;
                  productServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const productServiceRoutes = new ProductServiceRoutes();
export default productServiceRoutes;
