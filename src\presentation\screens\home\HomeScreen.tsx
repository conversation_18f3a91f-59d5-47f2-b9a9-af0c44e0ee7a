import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
  RefreshControl,
  Image,
  StatusBar,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAppDispatch, useAppSelector} from '../../store/store';
import {RootStackParamList} from '../../navigation/AppNavigator';
import MobileMenu from '../../components/MobileMenu';
import {
  getShowingCategoriesAsync,
  getShowingProductsOnHomePageCategoriesAsync,
  getBestSellersOfMonthAsync,
} from '../../store/slices/productSlice';
import {
  fetchAllSettingsAsync,
  selectGlobalSetting,
  selectLogo,
} from '../../store/slices/settingsSlice';
import {ELuxeColors} from '../../../theme/colors';
import {TextStyles} from '../../../theme/typography';

// Components (reproduction exacte du client web)
import SliderBanner from '../../components/SliderBanner'; // Comme Slider1
import Services from '../../components/Services'; // Comme Services
import TopCategories from '../../components/TopCategories'; // Comme Category
import ProductSlides from '../../components/ProductSlides'; // Comme ProductSlides
import WhiteProduct from '../../components/WhiteProduct'; // Comme WhiteProduct
// MobileMenu importé plus haut


type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const dispatch = useAppDispatch();

  const {} = useAppSelector(state => state.product);
  const globalSetting = useAppSelector(selectGlobalSetting);
  const logoFromSelector = useAppSelector(selectLogo);
  // Récupération EXACTEMENT comme le client web
  const {storeCustomizationSetting} = useAppSelector(
    (state: any) => state.setting,
  );

  // Logo priorité: navbar.logo > logo > fallback (comme HeaderSticky.tsx du client web)
  const logo =
    storeCustomizationSetting?.navbar?.logo ||
    storeCustomizationSetting?.logo ||
    logoFromSelector ||
    '';

  // Logique du panier (avec gestion d'erreur robuste)
  const cartState = useAppSelector(state => state.cart);
  const cartItems = cartState?.items || [];
  const cartItemsCount = cartItems.reduce((total, item) => {
    return total + (item?.quantity || 0);
  }, 0);

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [isMobileMenuVisible, setIsMobileMenuVisible] = useState(false);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      // 1. D'abord charger les settings (nécessaires pour Services)
      await dispatch(fetchAllSettingsAsync());

      // 2. Puis les autres données en parallèle (comme le client web)
      await Promise.all([
        // Categories showing - pour TopCategories.tsx
        dispatch(getShowingCategoriesAsync()),

        // Best sellers - pour WhiteProduct.tsx
        dispatch(getBestSellersOfMonthAsync()),

        // Categories avec produits - pour ProductSlides.tsx
        dispatch(getShowingProductsOnHomePageCategoriesAsync()),
      ]);
    } catch (error) {
      // Erreur silencieuse
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      navigation.navigate('Search', {query: searchQuery.trim()});
    }
  };

  const handleCategoryPress = (category: any) => {
    navigation.navigate('Shop', {
      categoryId: category.id || category._id,
      filters: { category: category.id || category._id },
    });
  };

  const handleProductPress = (product: any) => {
    navigation.navigate('ProductDetail', {
      productId: product.id || product._id,
    });
  };

  // Handler panier supprimé (accessible via bottom tab)

  const handleViewAllProducts = () => {
    navigation.navigate('Shop', {});
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        {/* Header avec Menu Mobile - Comme le client web */}
        <View style={styles.header}>
          {/* Bouton Menu Mobile (comme meanmenu-reveal du client web) */}
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => setIsMobileMenuVisible(true)}
          >
            <Icon
              name="menu"
              size={20}
              color={ELuxeColors.textPrimary}
            />
          </TouchableOpacity>

          {/* Logo et Titre */}
          <View style={styles.logoContainer}>
            {logo && logo.trim() !== '' ? (
              <Image
                source={{uri: logo}}
                style={styles.logo}
                resizeMode="contain"
                onError={() => {
                  // Erreur silencieuse
                }}
                onLoad={() => {
                  // Chargement silencieux
                }}
              />
            ) : (
              <View style={styles.logoPlaceholder}>
                <Text style={styles.logoPlaceholderText}>E-L</Text>
              </View>
            )}
            <Text style={styles.appTitle}>
              {globalSetting?.shop_name || 'E-Luxe'}
            </Text>
          </View>

          {/* Bouton Panier (comme le client web) */}
          <TouchableOpacity
            style={styles.cartButton}
            onPress={() => navigation.navigate('Cart' as any)}
          >
            <Icon
              name="shopping-cart"
              size={20}
              color={ELuxeColors.textPrimary}
            />
            {cartItemsCount > 0 && (
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{cartItemsCount}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Icon
              name="search"
              size={20}
              color={ELuxeColors.textSecondary}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search products..."
              placeholderTextColor={ELuxeColors.textTertiary}
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}>
                <Text style={styles.clearIcon}>✕</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Slider1 - Slider Banner */}
        <SliderBanner
          onSlidePress={() => {
            // Action silencieuse
          }}
        />

        {/* Services - Section services (EXACTEMENT comme le client web) */}
        <Services />

        {/* Category - Top Categories */}
        <TopCategories
          onCategoryPress={handleCategoryPress}
          title="Top Categories"
        />

        {/* ProductSlides - Catégories avec produits dynamiques */}
        <ProductSlides
          onProductPress={handleProductPress}
          onCategoryPress={handleCategoryPress}
        />

        {/* WhiteProduct - Meilleurs vendeurs */}
        <WhiteProduct
          onProductPress={handleProductPress}
          onViewAllPress={() => handleViewAllProducts()}
        />

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Menu Mobile - Comme le client web */}
      <MobileMenu
        isVisible={isMobileMenuVisible}
        onClose={() => setIsMobileMenuVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ELuxeColors.white, // Fond blanc pour meilleure visibilité globale
  },
  scrollView: {
    flex: 1,
    backgroundColor: ELuxeColors.grey2, // #F3F4F7 - Couleur E-Luxe pour le contenu
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ELuxeColors.white,
    shadowColor: ELuxeColors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  menuButton: {
    padding: 8,
    borderRadius: 8,
  },
  cartButton: {
    padding: 8,
    borderRadius: 8,
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: ELuxeColors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: ELuxeColors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Styles du menu supprimés (remplacé par bottom tabs)
  clearIcon: {
    fontSize: 16,
    color: ELuxeColors.textSecondary,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  logo: {
    width: 48, // Agrandi de 32 à 48 (50% plus grand)
    height: 48, // Agrandi de 32 à 48 (50% plus grand)
    marginRight: 12, // Augmenté pour équilibrer
  },
  logoPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ELuxeColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  logoPlaceholderText: {
    color: ELuxeColors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  appTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.primary, // Couleur or E-Luxe
    fontWeight: 'bold',
    fontSize: 20, // Taille plus grande pour meilleure visibilité
    textShadowColor: 'rgba(0, 0, 0, 0.1)', // Ombre légère pour meilleur contraste
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ELuxeColors.white,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.grey2, // Gris clair E-Luxe
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 44,
    borderWidth: 1,
    borderColor: ELuxeColors.border1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    ...TextStyles.input,
    color: ELuxeColors.textPrimary,
  },
  clearButton: {
    padding: 4,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default HomeScreen;
