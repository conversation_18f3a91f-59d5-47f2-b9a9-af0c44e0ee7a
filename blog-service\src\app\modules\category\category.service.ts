import Category from "./category.model";
import CategoryType from "./category.type";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-31
 *
 * Class CategoryService
 */
class CategoryService {
  /**
   * Get all posts categories
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-31
   *
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public getCategories(): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const categories = await Category.find().sort({ _id: -1 });

          resolve(categories);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Get all category parent and child
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-31
   *
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public getAllCategory(): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const categories: any = await Category.find().sort({
            _id: -1,
          });

          const categoryList =
            this.readyToParentAndChildrenCategory(categories);

          resolve(categoryList);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Create a category
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-07-31
   *
   * @param {CategoryType} data the request body
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async store(data: CategoryType): Promise<void> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const category: any = new Category(data);
          const createdCategory = await category.save();

          resolve(createdCategory);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Create many categories
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-19
   *
   * @param {Array<CategoryType>} data the request body
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async storeMany(data: Array<CategoryType>): Promise<void> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          // await Category.deleteMany();

          const createdCategories: any = await Category.insertMany(data);

          resolve(createdCategories);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Get category details
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-07-31
   *
   * @param {string} categoryId the category's id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public getCategoryById(categoryId: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const category = await Category.findById(categoryId);

          resolve(category);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Get showing categories details
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-19
   *
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public getShowingCategories(): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const categories: any = await Category.find({ status: "show" }).sort({
            _id: -1,
          });

          const categoryList =
            this.readyToParentAndChildrenCategory(categories);

          resolve(categoryList);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Update a category
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-01-07
   *
   * @param {string} categoryId the category id
   * @param {any} data the category data
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async update(categoryId: string, data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const category = await Category.findById(categoryId);

          if (category) {
            category.name = { ...category.name, ...data.name };
            category.description = {
              ...category.description,
              ...data.description,
            };
            category.slug = data.slug || category.slug;
            category.icon = data.icon || category.icon;
            category.status = data.status || category.status;
            category.parent_id = data.parentId || category.parent_id;
            category.parent_name = data.parentName || category.parent_name;

            const updatedCategory = await category.save();

            resolve(updatedCategory);
          } else {
            resolve(category);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Update category status
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-01-07
   *
   * @param {string} categoryId the category id
   * @param {any} data the category data
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async updateStatus(categoryId: string, data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const category = await Category.findById(categoryId);

          if (category) {
            category.status = data.status;

            const updatedCategory = await category.save();

            resolve(updatedCategory);
          } else {
            resolve(category);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Update many categories
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-01-07
   *
   * @param {any} data the categories data
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async updateMany(data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const updatedData: any = {};
          for (const key of Object.keys(data)) {
            if (
              data[key] !== "[]" &&
              Object.entries(data[key]).length > 0 &&
              data[key] !== data.ids
            ) {
              updatedData[key] = data[key];
            }
          }

          const updatedCategory = await Category.updateMany(
            { _id: { $in: data.ids } },
            {
              $set: updatedData,
            },
            {
              multi: true,
            }
          );

          resolve(updatedCategory);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Delete a category by id
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-07-31
   *
   * @param {string} categoryId the category id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public delete(categoryId: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const category: any = await Category.findById(categoryId);

          if (category) {
            const deletedCategory = await category.deleteOne();

            resolve(deletedCategory);
          } else {
            resolve(category);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Delete many categories
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-19
   *
   * @param {Array<string>} categoryIds the category ids.
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public deleteMany(categoryIds: Array<string>): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          // await Category.deleteMany({ parent_id: categoryIds.map((item) => item) });
          // Recursive function to delete subcategories
          const deleteSubcategories = async (ids: Array<string>) => {
            // Find subcategories for the given ids
            const subcategories = await Category.find({
              parent_id: { $in: ids },
            });

            // If there are subcategories, process them recursively
            if (subcategories.length > 0) {
              const subcategoryIds = subcategories.map((sub) =>
                sub._id.toString()
              );
              await deleteSubcategories(subcategoryIds); // Recursive call
            }

            // Delete all categories with the given ids
            await Category.deleteMany({ _id: { $in: ids } });
          };
          // const deletedCategory = await Category.deleteOne({
          //   _id: { $in: categoryIds },
          // });

          const deletedCategories = deleteSubcategories(categoryIds);

          resolve(deletedCategories);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Ready to parent and children category
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-19
   *
   * @param {Array<CategoryType>} categories - The categories.
   * @param {string} parentId - The parent id.
   * @return {Array<CategoryType>} - The eventual completion or failure.
   */
  private readyToParentAndChildrenCategory(
    categories: Array<CategoryType>,
    parentId = ""
  ): Array<CategoryType> {
    const categoryList: Array<any> = [];
    let Categories;
    if (!parentId) {
      Categories = categories.filter((cat) => cat.parent_id == undefined);
    } else {
      Categories = categories.filter((cat) => cat.parent_id == parentId);
    }

    for (const cate of Categories) {
      categoryList.push({
        _id: cate._id,
        name: cate.name,
        parent_id: cate.parent_id,
        parent_name: cate.parent_name,
        description: cate.description,
        icon: cate.icon,
        status: cate.status,
        children: this.readyToParentAndChildrenCategory(categories, cate._id),
      });
    }

    return categoryList;
  }
}

const categoryService = new CategoryService();
export default categoryService;
