import notificationSubscribe from "../modules/notification/notification.subscribe";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-09-16
 *
 * Class Subscribes
 */
class Subscribes {

  /**
   * Creating app Subscribes starts
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-09-16
   *
   * @returns {void}
   */
  public appSubscribes(): void {
    // Includes all subscribes
    notificationSubscribe.createOrderNotification();
  }

  /**
   * Load subscribes
   *
   * <AUTHOR> Mag<PERSON> <<EMAIL>>
   * @since 2023-08-27
   *
   * @returns {void}
   */
  public subscribesConfig(): void {
    this.appSubscribes();
  }
}

export default Subscribes;
