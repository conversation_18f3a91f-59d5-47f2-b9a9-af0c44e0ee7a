/**
 * WishlistSlice - Mobile E-Luxe 1.0
 * Gestion de la wishlist EXACTEMENT comme le client web
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface WishlistItem {
  id: string;
  productId: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  discount?: number;
  inStock: boolean;
  addedDate: string;
  brand?: string;
  variants?: any[];
}

interface WishlistState {
  wishlistItems: WishlistItem[];
  isLoading: boolean;
  error: string | null;
}

const initialState: WishlistState = {
  wishlistItems: [],
  isLoading: false,
  error: null,
};

// Async thunks pour l'API
export const addToWishlistAsync = createAsyncThunk(
  'wishlist/addToWishlist',
  async (productId: string, { rejectWithValue }) => {
    try {
      // TODO: Appel API réel
      console.log('💖 Ajout à la wishlist:', productId);
      return { productId, addedDate: new Date().toISOString() };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add to wishlist');
    }
  }
);

export const removeFromWishlistAsync = createAsyncThunk(
  'wishlist/removeFromWishlist',
  async (productId: string, { rejectWithValue }) => {
    try {
      // TODO: Appel API réel
      console.log('💔 Suppression de la wishlist:', productId);
      return productId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to remove from wishlist');
    }
  }
);

export const getWishlistAsync = createAsyncThunk(
  'wishlist/getWishlist',
  async (_, { rejectWithValue }) => {
    try {
      // TODO: Appel API réel
      console.log('📋 Récupération de la wishlist...');
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch wishlist');
    }
  }
);

const wishlistSlice = createSlice({
  name: 'wishlist',
  initialState,
  reducers: {
    addToWishlist: (state, action: PayloadAction<WishlistItem>) => {
      const existingItem = state.wishlistItems.find(item => item.productId === action.payload.productId);
      if (!existingItem) {
        state.wishlistItems.push(action.payload);
      }
    },
    removeFromWishlist: (state, action: PayloadAction<string>) => {
      state.wishlistItems = state.wishlistItems.filter(item => item.productId !== action.payload);
    },
    clearWishlist: (state) => {
      state.wishlistItems = [];
    },
    updateWishlistItem: (state, action: PayloadAction<{ productId: string; updates: Partial<WishlistItem> }>) => {
      const item = state.wishlistItems.find(item => item.productId === action.payload.productId);
      if (item) {
        Object.assign(item, action.payload.updates);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Add to wishlist
      .addCase(addToWishlistAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addToWishlistAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        // TODO: Ajouter l'item complet depuis l'API
      })
      .addCase(addToWishlistAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Remove from wishlist
      .addCase(removeFromWishlistAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(removeFromWishlistAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.wishlistItems = state.wishlistItems.filter(item => item.productId !== action.payload);
      })
      .addCase(removeFromWishlistAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Get wishlist
      .addCase(getWishlistAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getWishlistAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.wishlistItems = action.payload;
      })
      .addCase(getWishlistAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { addToWishlist, removeFromWishlist, clearWishlist, updateWishlistItem } = wishlistSlice.actions;
export default wishlistSlice.reducer;
