import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import statusCode from "../utils/status-code.util";
import authenticationController from "./authentication.controller";
import authorization from "../middlewares/authorization.middleware";
import jwtUtilities from "../utils/jwt-utilities.util";
import errorNumbers from "../utils/error-numbers.util";
import customResponse from "../utils/custom-response.util";
import config from "../../config";
import bodyParser from "body-parser";
import { formatErrorMessages } from "../utils/helpers.util";
import zlib from "zlib";

const userServiceProxy = httpProxy(config.userServiceUrl, {
  userResDecorator: function (proxyRes, proxyResData, userReq, userRes) {
    try {
      // Vérifier si la réponse est compressée
      const contentEncoding = proxyRes.headers['content-encoding'];

      // Map pour associer chaque encodage à la fonction de décompression
      const decompressionMethods: any = new Map([
        ['br', zlib.brotliDecompressSync],
        ['gzip', zlib.gunzipSync],
        ['deflate', zlib.inflateSync],
      ]);

      let dataString;

      if (contentEncoding && decompressionMethods.has(contentEncoding)) {
        dataString = decompressionMethods.get(contentEncoding)(proxyResData).toString('utf8');
      } else {
        dataString = proxyResData.toString('utf8');
      }

      let data;
      try {
        data = JSON.parse(dataString);
      } catch (jsonError) {
        console.error("Erreur de parsing JSON :", jsonError);
        return dataString; // Retourne la réponse brute si JSON invalide
      }

      // Ajout du token si la requête est un login et que le statut est 200
      if (userReq.url.includes("/login") && userRes.statusCode === statusCode.httpOk) {
        data.data = jwtUtilities.generateToken(data.data);
      }

      return JSON.stringify(data);
    } catch (error) {
      console.error("Erreur dans userResDecorator :", error);
      return proxyResData; // Retourne la réponse d'origine en cas d'erreur
    }
  },
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: formatErrorMessages(err.errors) || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-04-22
 *
 * Class AuthenticationRoutes
 */
class AuthenticationRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-03-26
   */
  constructor() {
    this.router = express.Router({ mergeParams: true });
  }

  // --------------------------------------------------------------------------
  // Public functions
  // --------------------------------------------------------------------------

  /**
   * Creating all authentication routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-04-22
   *
   * @returns {Router} of authentication
   */
  public authenticationRoutes(): Router {
    return this.router.use(
      "/auth",
      bodyParser.json(),
      routesGrouping.group((router) => {
        /**
         * @swagger
         * /v1/{lang}/auth/generate/keypair:
         *   get:
         *     tags:
         *     - Authentication
         *     operationId: keypair
         *     summary: Generate a public and private keypair.
         *     description: Generate a public and private keypair.
         *     parameters:
         *      - in: path
         *        name: lang
         *        schema:
         *          type: string
         *          example: en
         *        required: true
         *        description: Language for the response. Supported languages
         *          ['en', 'fr']
         *
         *     responses:
         *       200:
         *         description: The user has successfully logged in.
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 status:
         *                  type: string
         *                  example: Ok
         *                 data:
         *                  type: object
         *                  properties:
         *                    publicKey:
         *                     type: string
         *                     description: The public key.
         *                    privateKey:
         *                     type: string
         *                     description: The private key.
         *       '400':
         *         description: Bad Request.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/400'
         *
         *       '401':
         *         description: Unauthorized.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/401'
         *
         *       '412':
         *         description: Precondition Failed.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/412'
         *
         *       '500':
         *         description: Internal Server Error.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/500'
         *
         */
        router.get(
          "/generate/keypair",
          authenticationController.generateKeyPair
        );

        /**
         * @swagger
         * /v1/{lang}/auth/login:
         *   post:
         *     tags:
         *     - Authentication
         *     operationId: login
         *     summary: Logs user into the system (Get token).
         *     description: Logs user into the system (Get token).
         *     parameters:
         *      - in: path
         *        name: lang
         *        schema:
         *          type: string
         *          example: en
         *        required: true
         *        description: Language for the response. Supported languages
         *          ['en', 'fr']
         *
         *     requestBody:
         *       required: true
         *       content:
         *         application/x-www-form-urlencoded:
         *           schema:
         *             type: object
         *             properties:
         *               email:
         *                 type: string
         *                 description: The user's email.
         *                 example: <EMAIL>
         *               password:
         *                 type: string
         *                 format: password
         *                 description: The user's password.
         *                 example: admin
         *             required:
         *               - email
         *               - password
         *     responses:
         *       200:
         *         description: The user has successfully logged in.
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 status:
         *                  type: string
         *                  example: Ok
         *                 data:
         *                  type: object
         *                  properties:
         *                    accessToken:
         *                     type: string
         *                     description: Access token.
         *                    refreshToken:
         *                     type: string
         *                     description: Refresh token.
         *       '400':
         *         description: Bad Request.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/400'
         *
         *       '401':
         *         description: Unauthorized.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/401'
         *
         *       '412':
         *         description: Precondition Failed.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/412'
         *
         *       '500':
         *         description: Internal Server Error.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/500'
         *
         */
        router.post("/login", (req, res, next) => {
          const bearerToken = jwtUtilities.generateInternToken();

          // Set request header authorization with generic gateway
          req.headers.authorization = `Bearer ${bearerToken}`;

          // Update url with original url which contain all path
          req.url = `/v1/${
            JSON.parse(JSON.stringify(req.params)).lang
          }/users/login`;

          userServiceProxy(req, res, next);
        });

        /**
         * @swagger
         * /v1/{lang}/auth/logout:
         *   get:
         *     security:
         *      - bearerAuth: []
         *     tags:
         *     - Authentication
         *     operationId: logout
         *     summary: Logs out current logged in user (Destroy token).
         *     description: Logs out current logged in user (Destroy token).
         *     parameters:
         *      - in: path
         *        name: lang
         *        schema:
         *          type: string
         *          example: en
         *        required: true
         *        description: Language for the response. Supported languages
         *          ['en', 'fr']
         *
         *     responses:
         *       204:
         *         description: The user logout successfully.
         *
         *       '400':
         *         description: Bad Request.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/400'
         *
         *       '401':
         *         description: Unauthorized.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/401'
         *
         *       '404':
         *         description: Not Found.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/404'
         *
         *       '412':
         *         description: Precondition Failed.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/412'
         *
         *       '500':
         *         description: Internal Server Error.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/500'
         *
         */
        router.get(
          "/logout",
          // authorization.isAuth,
          authenticationController.logout
        );

        /**
         * @swagger
         * /v1/{lang}/auth/refresh:
         *   post:
         *     security:
         *      - bearerAuth: []
         *     tags:
         *     - Authentication
         *     operationId: refresh
         *     summary: Refresh acces token.
         *     description: Refresh acces token.
         *     parameters:
         *      - in: path
         *        name: lang
         *        schema:
         *          type: string
         *          example: en
         *        required: true
         *        description: Language for the response. Supported languages
         *          ['en', 'fr']
         *
         *     requestBody:
         *       required: true
         *       content:
         *         application/x-www-form-urlencoded:
         *           schema:
         *             type: object
         *             properties:
         *               refreshToken:
         *                 type: string
         *                 description: The refresh token.
         *             required:
         *               - refreshToken
         *     responses:
         *       200:
         *         description: The user has successfully logged in.
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 status:
         *                  type: string
         *                  example: Ok
         *                 data:
         *                  type: object
         *                  properties:
         *                    accessToken:
         *                     type: string
         *                     description: Access token.
         *                    refreshToken:
         *                     type: string
         *                     description: Refresh token.
         *       '400':
         *         description: Bad Request.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/400'
         *
         *       '401':
         *         description: Unauthorized.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/401'
         *
         *       '412':
         *         description: Precondition Failed.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/412'
         *
         *       '500':
         *         description: Internal Server Error.
         *         content:
         *          application/json:
         *             schema:
         *              $ref: '#/responses/schemas/500'
         *
         */
        router.post(
          "/refresh",
          authorization.isAuth,
          authenticationController.refresh
        );
      })
    );
  }
}

const authenticationRoutes = new AuthenticationRoutes();
export default authenticationRoutes;
