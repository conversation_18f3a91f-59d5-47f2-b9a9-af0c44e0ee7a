import { Server as HTTPServer } from "http";
import { Server as SocketIOServer, Socket } from "socket.io";

/**
 * Classe gérant les connexions WebSocket via Socket.IO.
 *
 * Cette classe initialise un serveur WebSocket et écoute les événements
 * de connexion, de message et de déconnexion des clients.
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
class SocketService {
  private io: SocketIOServer;

  /**
   * Initialise une instance de Socket.IO avec la configuration du serveur HTTP.
   *
   * @param {HTTPServer} server - Le serveur HTTP sur lequel attacher Socket.IO.
   */
  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: "*", // À adapter selon les besoins
        methods: ["PUT", "GET", "POST", "DELETE", "PATCH", "OPTIONS"],
      },
    });

    this.setupListeners();
  }

  /**
   * Configure les écouteurs d'événements WebSocket.
   *
   * Gère :
   * - La connexion d'un client.
   * - La réception de messages.
   * - La déconnexion d'un client.
   * <AUTHOR> <<EMAIL>>
   * @since 2025-03-29
   * @returns {void}
   */
  private setupListeners(): void {
    this.io.on("connection", (socket: Socket) => {
      console.log(`Client connecté : ${socket.id}`);

      /**
       * Événement déclenché lorsqu'un client envoie un message.
       *
       * @param {any} data - Le message reçu du client.
       */
      socket.on("notification", (data) => {
        console.log(`Notification reçu de ${socket.id} :`, data);
        this.io.emit("notification", data); // Broadcast du message
      });

      /**
       * Événement déclenché lorsqu'un client se déconnecte.
       */
      socket.on("disconnect", () => {
        console.log(`Client déconnecté : ${socket.id}`);
      });
    });
  }

  /**
   * Retourne l'instance de Socket.IO.
   *
   * @returns {SocketIOServer} L'instance du serveur WebSocket.
   */
  public getIO(): SocketIOServer {
    return this.io;
  }
}

export default SocketService;
