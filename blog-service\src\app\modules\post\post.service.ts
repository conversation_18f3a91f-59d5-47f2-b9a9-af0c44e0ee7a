import { Request } from "express";
import Category from "../category/category.model";
import Tag from "../tag/tag.model";
import Post from "./post.model";
import * as jsonpatch from "fast-json-patch";
import PostType from "./post.type";
// import languageCodes from "../../../resources/data/data";
import mongoose from "mongoose";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-06-21
 *
 * Class PostService
 */
class PostService {
  /**
   * Show posts details by filter
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-06-21
   *
   * @param {Request} req the http request
   *
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public showPostsByFilter(req: Request): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const page: number = Number(req.query.page) || 1;
          const pageSize: number = Number(req.query.perPage) || 12;
          const name: any = req.query.name || "";
          const featured: any = req.query.featured || "";
          const promotional: any = req.query.promotional || "";
          const category: any = req.query.category
            ? await Category.findById(req.query.category)
            : "";
          const tag: any = req.query.tag
            ? await Tag.findById(req.query.tag)
            : "";
          const vendor: any = req.query.vendor || "";
          const order: any = req.query.order || "";
          const min: number =
            req.query.min && Number(req.query.min) !== 0
              ? Number(req.query.min)
              : 0;
          const max: number =
            req.query.max && Number(req.query.max) !== 0
              ? Number(req.query.max)
              : 0;
          const rating: any =
            req.query.rating && Number(req.query.rating) !== 0
              ? Number(req.query.rating)
              : 0;

          const nameFilter: any = name
            ? { [`title.${req.params.lang}`]: { $regex: name, $options: "i" } }
            : {};
          const featuredFilter: any = featured
            ? { featured: { $gte: featured } }
            : {};
          const promotionalFilter: any = promotional
            ? { promotional: { $gte: promotional } }
            : {};
          const vendorFilter: any = vendor ? { vendor } : {};
          const priceFilter: any =
            min && max ? { price: { $gte: min, $lte: max } } : {};
          const ratingFilter: any = rating ? { rating: { $gte: rating } } : {};
          const categoryFilter: any = category
            ? { categories: category._id }
            : {};
          const tagFilter: any = tag ? { tags: tag._id } : {};

          // Separate filter conditions
          const filter = {
            ...vendorFilter,
            ...nameFilter,
            ...categoryFilter,
            ...tagFilter,
            ...priceFilter,
            ...ratingFilter,
            ...featuredFilter,
            ...promotionalFilter,
          };

          // Apply status filters separately
          if (order === "published") {
            filter.status = "show";
          } else if (order === "unPublished") {
            filter.status = "hide";
          } else if (order === "status-selling") {
            filter.current_stock = { $gt: 0 };
          } else if (order === "status-out-of-stock") {
            filter.current_stock = { $lt: 1 };
          }

          const sortOrder: any =
            order === "lowest"
              ? { "prices.original_price": 1 }
              : order === "highest"
              ? { "prices.original_price": -1 }
              : order === "date-added-desc"
              ? { created_at: -1 }
              : order === "date-updated-asc"
              ? { updated_at: 1 }
              : order === "date-updated-desc"
              ? { updated_at: -1 }
              : order === "toprated"
              ? { rating: -1 }
              : { _id: -1 };
          const count: any = await Post.countDocuments(filter);

          const posts: any = await Post.find(filter)
            .populate("categories", "_id name slug")
            .populate("category", "_id name slug")
            .populate("tags", "name slug")
            .sort(sortOrder)
            .skip(pageSize * (page - 1))
            .limit(pageSize);

          const previousPage: any = page === 1 ? null : page - 1;
          const currentPage: number = page;
          const pages: number = Math.ceil(count / pageSize);
          const nextPage: any = pages > page ? page + 1 : null;

          resolve({
            posts,
            previousPage,
            perPage: pageSize,
            allPosts: count,
            currentPage,
            pages,
            nextPage,
          });
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * get all posts shown
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-21
   *
   * @return {Promise<any>} the eventual completion or failure
   */
  public getShowingPosts(): Promise<any> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const posts = await Post.find({ status: "show" }).sort({
            _id: -1,
          });

          resolve(posts);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Get showing store posts
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-21
   *
   * @param {string} category - The post default category.
   * @param {string} title - The post title.
   * @param {string} slug - The post slug.
   * @return {Promise<any>} the eventual completion or failure
   */
  public getShowingStorePosts(
    category: string,
    title: string,
    slug: string
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const queryObject: any = { status: "show" };

          if (category) {
            queryObject.categories = {
              $in: [category],
            };
          }

          // if (title) {
          //   const titleQueries = languageCodes.map((lang) => ({
          //     [`title.${lang}`]: { $regex: `${title}`, $options: "i" },
          //   }));

          //   queryObject.$or = titleQueries;
          // }
          if (slug) {
            queryObject.slug = { $regex: slug, $options: "i" };
          }

          let posts: any = [];
          let popularPosts: any = [];
          let discountedPosts: any = [];
          let relatedPosts: any = [];

          if (slug) {
            posts = await Post.find(queryObject)
              .populate({ path: "category", select: "name _id" })
              .sort({ _id: -1 })
              .limit(100);
            relatedPosts = await Post.find({
              category: posts[0]?.category,
            }).populate({ path: "category", select: "_id name" });
          } else if (title || category) {
            posts = await Post.find(queryObject)
              .populate({ path: "category", select: "name _id" })
              .sort({ _id: -1 })
              .limit(100);
          } else {
            popularPosts = await Post.find({ status: "show" })
              .populate({ path: "category", select: "name _id" })
              .sort({ sales: -1 })
              .limit(20);

            discountedPosts = await Post.find({
              status: "show", // Ensure status "show" for discounted posts
              $or: [
                {
                  $and: [
                    { isCombination: true },
                    {
                      variants: {
                        $elemMatch: {
                          discount: { $gt: "0.00" },
                        },
                      },
                    },
                  ],
                },
                {
                  $and: [
                    { isCombination: false },
                    {
                      $expr: {
                        $gt: [
                          { $toDouble: "$prices.discount" }, // Convert the discount field to a double
                          0,
                        ],
                      },
                    },
                  ],
                },
              ],
            })
              .populate({ path: "category", select: "name _id" })
              .sort({ _id: -1 })
              .limit(20);
          }

          resolve({
            posts,
            popularPosts,
            relatedPosts,
            discountedPosts,
          });
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Create a posts
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-06-24
   *
   * @param {any} data the request body
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async store(data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post = new Post({
            ...data,
            name: data.title.en || data.title.fr,
            // postId: cname + (count + 1),
            post_id: data.post_id
              ? data.post_id
              : new mongoose.Types.ObjectId(),
          });

          const createdPost: any = await post.save();

          resolve(createdPost);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Create a review
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-10-08
   *
   * @param {string} postId the post id
   * @param {any} data the update object
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async createReview(postId: string, data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post = await Post.findById(postId);

          if (post) {
            let updatedPost: any = null;
            const review = {
              name: data.name,
              email: data.email,
              rating: Number(data.rating),
              comment: data.comment,
            };

            if (post.reviews.find((x: any) => x.email === data.email)) {
              post.reviews.map((x: any) => {
                if (x.email === data.email) {
                  x.rating = review.rating;
                  x.comment = review.comment;
                }
              });

              updatedPost = await post.save();
            } else {
              post.reviews.push(review);
              post.num_reviews = post.reviews.length;
              post.rating =
                post.reviews.reduce((a: any, c: any) => c.rating + a, 0) /
                post.reviews.length;

              updatedPost = await post.save();
            }

            resolve(updatedPost);
          } else {
            resolve(post);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Create multiple posts
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-06-22
   *
   * @param {Array<PostType>} data the data relating to the post to be stored.
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async storeMultiple(data: Array<PostType>): Promise<void> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const createdPosts: any = await Post.insertMany(data);
          resolve(createdPosts);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Get post details
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-06-24
   *
   * @param {string} postId the post's id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public showPostById(postId: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post = await Post.findById(postId)
            .populate("categories", "_id  name")
            .populate("tags", "name slug")
            .populate("related_posts")
            .populate("category", "_id name");

          resolve(post);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Get post by slug
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-21
   *
   * @param {string} postSlug the post's slug.
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public getPostBySlug(postSlug: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post = await Post.findOne({ slug: postSlug })
            .populate("categories", "name slug")
            .populate("tags", "name slug")
            .populate("related_posts");

          resolve(post);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Assign a category to a post
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-01
   *
   * @param {string} postId the post id
   * @param {string} categoryId the category id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public assignToCategory(
    postId: string,
    categoryId: string
  ): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post: any = await Post.findById(postId);

          if (post) {
            const category: any = await Category.findById(categoryId);

            if (category) {
              // Check if the post doesn't already have this category
              if (post.categories.includes(category._id))
                resolve("ALREADY_ASSIGNED");
              else {
                post.categories = [...post.categories, category._id];
                await post.save();

                category.posts = [...category.posts, post._id];
                await category.save();
              }

              resolve(post);
            } else {
              resolve("CATEGORY_NOT_FOUND");
            }
          } else {
            resolve("PRODUCT_NOT_FOUND");
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Unassign a category to a post
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-01
   *
   * @param {string} postId the post id
   * @param {string} categoryId the category id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public unassignFromCategory(
    postId: string,
    categoryId: string
  ): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post: any = await Post.findById(postId);

          if (post) {
            const category: any = await Category.findById(categoryId);

            if (category) {
              // Check if the post have this category
              if (!post.categories.includes(category._id))
                resolve("NOT_HAVE_THIS_CATERY");
              else {
                post.categories = post.categories.filter(
                  (x: any) => x.toString() != category._id.toString()
                );

                await post.save();

                category.posts = category.posts.filter(
                  (x: any) => x.toString() != post._id.toString()
                );

                await category.save();
              }

              resolve(post);
            } else {
              resolve("CATEGORY_NOT_FOUND");
            }
          } else {
            resolve("PRODUCT_NOT_FOUND");
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Assign a tag to a post
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-01
   *
   * @param {string} postId the post id
   * @param {string} tagId the tag id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public assignToTag(postId: string, tagId: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post: any = await Post.findById(postId);

          if (post) {
            const tag: any = await Tag.findById(tagId);

            if (tag) {
              // Check if the post doesn't already have this tag
              if (post.tags.includes(tag._id)) resolve("ALREADY_ASSIGNED");
              else {
                post.tags = [...post.tags, tag._id];
                await post.save();

                tag.posts = [...tag.posts, post._id];
                await tag.save();
              }

              resolve(post);
            } else {
              resolve("TAG_NOT_FOUND");
            }
          } else {
            resolve("PRODUCT_NOT_FOUND");
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Unassign a tag to a post
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-01
   *
   * @param {string} postId the post id
   * @param {string} tagId the tag id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public unassignFromTag(postId: string, tagId: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post: any = await Post.findById(postId);

          if (post) {
            const tag: any = await Tag.findById(tagId);

            if (tag) {
              // Check if the post have this tag
              if (!post.tags.includes(tag._id)) resolve("NOT_HAVE_THIS_TAG");
              else {
                post.tags = post.tags.filter(
                  (x: any) => x.toString() != tag._id.toString()
                );

                await post.save();

                tag.posts = tag.posts.filter(
                  (x: any) => x.toString() != post._id.toString()
                );

                await tag.save();
              }

              resolve(post);
            } else {
              resolve("TAG_NOT_FOUND");
            }
          } else {
            resolve("PRODUCT_NOT_FOUND");
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Update a post
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-21
   *
   * @param {string} postId the post id.
   * @param {any} data the post data.
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async update(postId: string, data: PostType): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post = await Post.findById(postId);
          // console.log("post", post);

          if (post) {
            post.title = { ...post.title, ...data.title };
            post.description = {
              ...post.description,
              ...data.description,
            };

            post.slug = data.slug;
            post.categories = data.categories;
            post.category = data.category;
            post.status = data.status;
            post.image = data.image;
            post.tags = data.tags;

            await post.save();
            resolve(post);
          } else {
            resolve(post);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Update many posts
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-21
   *
   * @param {any} data the post data.
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async updateMany(data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const updatedData: any = {};
          for (const key of Object.keys(data)) {
            if (
              data[key] !== "[]" &&
              Object.entries(data[key]).length > 0 &&
              data[key] !== data.ids
            ) {
              // console.log('req.body[key]', typeof req.body[key]);
              updatedData[key] = data[key];
            }
          }

          // console.log("updated data", updatedData);

          const posts = await Post.updateMany(
            { _id: { $in: data.ids.map((x: string) => x) } },
            {
              $set: updatedData,
            },
            {
              multi: true,
            }
          );

          resolve(posts);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Patch a post
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-22
   *
   * @param {string} postId the post id
   * @param {any} data the update object
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public async patch(postId: string, data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post = await Post.findById(postId);

          if (post) {
            const updateObject = jsonpatch.applyPatch(
              post.toObject(),
              data,
              false,
              true
            ).newDocument;

            await Post.updateOne({ _id: postId }, { $set: updateObject });

            resolve(updateObject);
          } else {
            resolve(post);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Delete a post by id
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-08-01
   *
   * @param {string} postId the post id
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public delete(postId: string): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const post: any = await Post.findById(postId);

          if (post) {
            const deletePost = await post.deleteOne();

            resolve(deletePost);
          } else {
            resolve(post);
          }
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  /**
   * Delete many posts
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2024-07-21
   *
   * @param {Array<string>} postIds the post ids
   * @return {Promise<unknown>} the eventual completion or failure
   */
  public deleteMany(postIds: Array<string>): Promise<unknown> {
    return new Promise((resolve, reject) => {
      (async () => {
        try {
          const deletePosts = await Post.deleteMany({
            _id: { $in: postIds },
          });

          resolve(deletePosts);
        } catch (error) {
          reject(error);
        }
      })();
    });
  }
}

const postService = new PostService();
export default postService;
