/**
 * DealProduct - Mobile E-Luxe 1.0
 * Reproduction exacte du composant DealProduct1 du client web
 * Affiche les produits en promotion avec countdown timer
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../store/store';
import { getDealsAsync } from '../store/slices/productSlice';
import { ELuxeColors, ComponentColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface DealProductProps {
  onProductPress?: (product: any) => void;
  onViewAllPress?: () => void;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const DealProduct: React.FC<DealProductProps> = ({
  onProductPress,
  onViewAllPress,
}) => {
  const dispatch = useAppDispatch();
  const { products, isLoading } = useAppSelector((state) => state.product);
  
  // Countdown timer state
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 2,
    hours: 14,
    minutes: 30,
    seconds: 45,
  });

  useEffect(() => {
    loadDeals();
  }, []);

  useEffect(() => {
    // Countdown timer
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        let { days, hours, minutes, seconds } = prev;
        
        if (seconds > 0) {
          seconds--;
        } else if (minutes > 0) {
          minutes--;
          seconds = 59;
        } else if (hours > 0) {
          hours--;
          minutes = 59;
          seconds = 59;
        } else if (days > 0) {
          days--;
          hours = 23;
          minutes = 59;
          seconds = 59;
        }
        
        return { days, hours, minutes, seconds };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const loadDeals = async () => {
    try {
      await dispatch(getDealsAsync()).unwrap();
    } catch (error) {
      console.error('Erreur lors du chargement des offres:', error);
    }
  };

  const handleProductPress = (product: any) => {
    onProductPress?.(product);
  };

  const renderCountdownItem = (value: number, label: string) => (
    <View style={styles.countdownItem}>
      <View style={styles.countdownValue}>
        <Text style={styles.countdownNumber}>{value.toString().padStart(2, '0')}</Text>
      </View>
      <Text style={styles.countdownLabel}>{label}</Text>
    </View>
  );

  const renderDealProduct = (product: any, index: number) => (
    <TouchableOpacity
      key={product.id || product._id}
      style={[styles.productCard, index === 0 && styles.featuredProduct]}
      onPress={() => handleProductPress(product)}
    >
      {/* Product Image */}
      <View style={styles.productImageContainer}>
        <Image
          source={{
            uri: product.image || 'https://via.placeholder.com/200x200/D4AF37/FFFFFF?text=Deal',
          }}
          style={styles.productImage}
          resizeMode="cover"
        />
        
        {/* Discount Badge */}
        {product.discount && (
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>-{product.discount}%</Text>
          </View>
        )}
        
        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.quickActionButton}>
            <Icon name="favorite-border" size={16} color={ELuxeColors.white} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickActionButton}>
            <Icon name="compare-arrows" size={16} color={ELuxeColors.white} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Product Info */}
      <View style={styles.productInfo}>
        <Text style={styles.productBrand}>{product.brand || 'E-Luxe'}</Text>
        <Text style={styles.productName} numberOfLines={2}>
          {product.title || product.name}
        </Text>
        
        {/* Rating */}
        <View style={styles.ratingContainer}>
          <View style={styles.stars}>
            {[1, 2, 3, 4, 5].map((star) => (
              <Icon
                key={star}
                name={star <= (product.rating || 0) ? 'star' : 'star-border'}
                size={12}
                color="#FFD700"
              />
            ))}
          </View>
          <Text style={styles.ratingText}>({product.reviewCount || 0})</Text>
        </View>

        {/* Price */}
        <View style={styles.priceContainer}>
          <Text style={styles.currentPrice}>${product.price}</Text>
          {product.originalPrice && product.originalPrice > product.price && (
            <Text style={styles.originalPrice}>${product.originalPrice}</Text>
          )}
        </View>

        {/* Add to Cart Button */}
        <TouchableOpacity style={styles.addToCartButton}>
          <Icon name="add-shopping-cart" size={16} color={ELuxeColors.white} />
          <Text style={styles.addToCartText}>Add to Cart</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Section Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.sectionTitle}>Deal of the Day</Text>
          <Text style={styles.sectionSubtitle}>Limited time offers</Text>
        </View>
        
        <TouchableOpacity style={styles.viewAllButton} onPress={onViewAllPress}>
          <Text style={styles.viewAllText}>View All</Text>
          <Icon name="arrow-forward" size={16} color={ELuxeColors.primary} />
        </TouchableOpacity>
      </View>

      {/* Countdown Timer */}
      <View style={styles.countdownContainer}>
        <View style={styles.countdownHeader}>
          <Icon name="access-time" size={20} color={ELuxeColors.white} />
          <Text style={styles.countdownTitle}>Hurry up! Offer ends in:</Text>
        </View>
        
        <View style={styles.countdown}>
          {renderCountdownItem(timeLeft.days, 'Days')}
          <Text style={styles.countdownSeparator}>:</Text>
          {renderCountdownItem(timeLeft.hours, 'Hours')}
          <Text style={styles.countdownSeparator}>:</Text>
          {renderCountdownItem(timeLeft.minutes, 'Min')}
          <Text style={styles.countdownSeparator}>:</Text>
          {renderCountdownItem(timeLeft.seconds, 'Sec')}
        </View>
      </View>

      {/* Products Grid */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.productsContainer}
      >
        {products.slice(0, 6).map((product, index) => renderDealProduct(product, index))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ELuxeColors.white,
    paddingVertical: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  sectionTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textPrimary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAllText: {
    ...TextStyles.body,
    color: ELuxeColors.primary,
    fontWeight: '600',
  },
  countdownContainer: {
    backgroundColor: '#FF6B6B',
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  countdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  countdownTitle: {
    ...TextStyles.body,
    color: ELuxeColors.white,
    fontWeight: '600',
  },
  countdown: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  countdownItem: {
    alignItems: 'center',
  },
  countdownValue: {
    backgroundColor: ELuxeColors.white,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 50,
    alignItems: 'center',
  },
  countdownNumber: {
    ...TextStyles.h4,
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  countdownLabel: {
    ...TextStyles.caption,
    color: ELuxeColors.white,
    marginTop: 4,
  },
  countdownSeparator: {
    ...TextStyles.h4,
    color: ELuxeColors.white,
    fontWeight: 'bold',
  },
  productsContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  productCard: {
    backgroundColor: ELuxeColors.white,
    borderRadius: 12,
    width: 180,
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: ELuxeColors.border2,
  },
  featuredProduct: {
    borderColor: ELuxeColors.primary,
    borderWidth: 2,
  },
  productImageContainer: {
    position: 'relative',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 140,
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  discountText: {
    ...TextStyles.caption,
    color: ELuxeColors.white,
    fontWeight: 'bold',
  },
  quickActions: {
    position: 'absolute',
    top: 8,
    right: 8,
    gap: 4,
  },
  quickActionButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 16,
    padding: 6,
  },
  productInfo: {
    padding: 12,
  },
  productBrand: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
    marginBottom: 4,
  },
  productName: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
    fontWeight: '600',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 4,
  },
  stars: {
    flexDirection: 'row',
  },
  ratingText: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  currentPrice: {
    ...TextStyles.h5,
    color: ELuxeColors.primary,
    fontWeight: 'bold',
  },
  originalPrice: {
    ...TextStyles.caption,
    color: ELuxeColors.textTertiary,
    textDecorationLine: 'line-through',
  },
  addToCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primary,
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
    gap: 6,
    justifyContent: 'center',
  },
  addToCartText: {
    ...TextStyles.caption,
    color: ELuxeColors.white,
    fontWeight: '600',
  },
});

export default DealProduct;
