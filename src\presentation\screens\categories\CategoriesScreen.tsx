import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { RootStackParamList } from '../../navigation/AppNavigator';

type CategoriesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface Category {
  id: string;
  _id?: string;
  name: string | { en: string; fr: string };
  image: string;
  productCount?: number;
  product_count?: number; // Comme l'API backend
  subcategories?: Category[];
}

// Fonction utilitaire pour gérer les traductions multilingues
const getTranslatedText = (text: string | { en: string; fr: string }, defaultLang = 'en'): string => {
  if (typeof text === 'string') {
    return text;
  }
  if (typeof text === 'object' && text !== null) {
    return text[defaultLang] || text.en || text.fr || '';
  }
  return '';
};

const CategoriesScreen: React.FC = () => {
  const navigation = useNavigation<CategoriesScreenNavigationProp>();
  const dispatch = useAppDispatch();
  
  const { categories, isLoading } = useAppSelector((state) => state.product);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);

  // SUPPRIMÉ: Données mockées - utilisation exclusive de l'API
  // Les catégories viennent de Redux store via l'API

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    filterCategories();
  }, [searchQuery, categories]);

  const loadCategories = async () => {
    try {
      // TODO: Dispatch action to load categories
      // await dispatch(getCategoriesAsync());
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const filterCategories = () => {
    if (!searchQuery.trim()) {
      setFilteredCategories(categories);
      return;
    }

    const filtered = categories.filter(category =>
      getTranslatedText(category.name).toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.subcategories?.some(sub =>
        getTranslatedText(sub.name).toLowerCase().includes(searchQuery.toLowerCase())
      )
    );

    setFilteredCategories(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  const handleCategoryPress = (category: Category) => {
    navigation.navigate('ProductList', {
      categoryId: category.id,
      title: getTranslatedText(category.name),
    });
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item)}
    >
      {/* Gestion d'image EXACTEMENT comme le client web (CategoryTable.tsx) */}
      <FastImage
        source={{
          uri: (item.image && item.image.trim() !== '')
            ? item.image
            : 'https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png'
        }}
        style={styles.categoryImage}
        resizeMode={FastImage.resizeMode.cover}
        onError={() => {
          console.log('⚠️ Erreur chargement image catégorie:', getTranslatedText(item.name));
        }}
      />
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{getTranslatedText(item.name)}</Text>
        <Text style={styles.productCount}>
          {(item.productCount || item.product_count || 0)} products
        </Text>
        
        {item.subcategories && item.subcategories.length > 0 && (
          <View style={styles.subcategoriesContainer}>
            {item.subcategories.slice(0, 3).map((sub, index) => (
              <Text key={sub.id} style={styles.subcategoryText}>
                {getTranslatedText(sub.name)}
                {index < Math.min(item.subcategories!.length, 3) - 1 && ' • '}
              </Text>
            ))}
            {item.subcategories.length > 3 && (
              <Text style={styles.moreText}>+{item.subcategories.length - 3} more</Text>
            )}
          </View>
        )}
      </View>
      <Icon name="chevron-right" size={24} color="#C7C7CC" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search categories..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <Icon name="clear" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories List */}
      <FlatList
        data={filteredCategories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id || item._id || Math.random().toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />

      {filteredCategories.length === 0 && searchQuery.length > 0 && (
        <View style={styles.emptyContainer}>
          <Icon name="search-off" size={64} color="#C7C7CC" />
          <Text style={styles.emptyTitle}>No categories found</Text>
          <Text style={styles.emptySubtitle}>
            Try searching with different keywords
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
  },
  clearButton: {
    padding: 4,
  },
  listContainer: {
    paddingVertical: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  productCount: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 6,
  },
  subcategoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  subcategoryText: {
    fontSize: 12,
    color: '#007AFF',
  },
  moreText: {
    fontSize: 12,
    color: '#999999',
    fontStyle: 'italic',
  },
  separator: {
    height: 1,
    backgroundColor: 'transparent',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CategoriesScreen;
