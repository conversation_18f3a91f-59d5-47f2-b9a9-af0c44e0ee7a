/**
 * Test de Connectivité API pour Mobile E-Luxe
 * Teste les endpoints utilisés par ProductSlides
 */

const testApiEndpoints = async () => {
  console.log('🔍 Test de connectivité API Mobile E-Luxe...\n');

  const baseUrl = 'http://localhost:2000';
  const endpoints = [
    '/v1/en/categories/showing',
    '/v1/en/categories/showingProductsOnHomePage',
    '/v1/en/products',
    '/v1/en/products/showing',
    '/v1/en/setting/global',
    '/v1/en/setting/store/customization',
  ];

  for (const endpoint of endpoints) {
    const url = `${baseUrl}${endpoint}`;
    console.log(`🌐 Test: ${url}`);
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        timeout: 10000,
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS: ${endpoint} - Status: ${response.status}`);
        
        if (Array.isArray(data)) {
          console.log(`   📊 Array avec ${data.length} éléments`);
          if (data.length > 0) {
            console.log(`   📝 Premier élément:`, Object.keys(data[0]).join(', '));
          }
        } else if (data && typeof data === 'object') {
          console.log(`   📊 Objet avec clés:`, Object.keys(data).join(', '));
        } else {
          console.log(`   📊 Type:`, typeof data);
        }
      } else {
        console.log(`❌ ERROR: ${endpoint} - Status: ${response.status}`);
        const errorText = await response.text();
        console.log(`   💥 Error: ${errorText.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`💥 NETWORK ERROR: ${endpoint}`);
      console.log(`   🚫 Error: ${error.message}`);
    }
    
    console.log(''); // Ligne vide
  }

  console.log('🏁 Test terminé.\n');
  
  // Test spécifique pour ProductSlides
  console.log('🧪 Test spécifique ProductSlides...\n');
  
  try {
    // Test 1: Récupération des catégories showing
    console.log('📂 Test: Catégories showing');
    const categoriesResponse = await fetch(`${baseUrl}/v1/en/categories/showing`);
    
    if (categoriesResponse.ok) {
      const categories = await categoriesResponse.json();
      console.log(`✅ ${categories.length} catégories récupérées`);
      
      if (categories.length > 0) {
        const firstCategory = categories[0];
        console.log(`📝 Première catégorie:`, firstCategory.name || firstCategory.title);
        
        // Test 2: Récupération des produits pour cette catégorie
        console.log(`\n📦 Test: Produits pour catégorie ${firstCategory._id || firstCategory.id}`);
        const productsResponse = await fetch(
          `${baseUrl}/v1/en/products?category=${firstCategory._id || firstCategory.id}&limit=5`
        );
        
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          const products = productsData.products || productsData.data || productsData;
          console.log(`✅ ${products.length} produits trouvés`);
          
          if (products.length > 0) {
            console.log(`📝 Premier produit:`, products[0].name || products[0].title);
          }
        } else {
          console.log(`❌ Erreur produits: ${productsResponse.status}`);
        }
      }
    } else {
      console.log(`❌ Erreur catégories: ${categoriesResponse.status}`);
    }
    
  } catch (error) {
    console.log(`💥 Erreur test ProductSlides:`, error.message);
  }

  console.log('\n📋 Diagnostic:');
  console.log('1. Si tous les tests échouent: API Gateway non démarré sur localhost:2000');
  console.log('2. Si catégories OK mais produits KO: Problème de filtrage par catégorie');
  console.log('3. Si tout fonctionne: Problème dans le code React Native');
  console.log('\n🚀 Solutions:');
  console.log('- Démarrer l\'API Gateway: npm start dans le dossier API');
  console.log('- Vérifier les CORS si erreur réseau');
  console.log('- Vérifier les logs React Native pour plus de détails');
};

// Simulation des données pour test offline
const getMockData = () => {
  return {
    categories: [
      {
        _id: '1',
        id: '1',
        name: 'Electronics',
        title: 'Electronics',
        image: 'https://via.placeholder.com/150x150/FFD700/000000?text=Electronics',
      },
      {
        _id: '2',
        id: '2',
        name: 'Fashion',
        title: 'Fashion',
        image: 'https://via.placeholder.com/150x150/C0C0C0/000000?text=Fashion',
      },
      {
        _id: '3',
        id: '3',
        name: 'Home & Garden',
        title: 'Home & Garden',
        image: 'https://via.placeholder.com/150x150/87CEEB/000000?text=Home',
      },
    ],
    products: [
      {
        id: '1',
        _id: '1',
        name: 'Luxury Smartphone',
        title: 'Luxury Smartphone',
        price: 999.99,
        originalPrice: 1199.99,
        discount: 17,
        image: 'https://via.placeholder.com/300x300/FFD700/000000?text=Phone',
        images: ['https://via.placeholder.com/300x300/FFD700/000000?text=Phone'],
        category: { _id: '1', name: 'Electronics' },
        rating: 4.5,
        reviewCount: 128,
      },
      {
        id: '2',
        _id: '2',
        name: 'Designer Watch',
        title: 'Designer Watch',
        price: 599.99,
        originalPrice: 799.99,
        discount: 25,
        image: 'https://via.placeholder.com/300x300/C0C0C0/000000?text=Watch',
        images: ['https://via.placeholder.com/300x300/C0C0C0/000000?text=Watch'],
        category: { _id: '2', name: 'Fashion' },
        rating: 4.8,
        reviewCount: 89,
      },
    ],
  };
};

console.log('🎯 Données de test disponibles:');
console.log('- getMockData() pour données offline');
console.log('- testApiEndpoints() pour test connectivité\n');

// Exécuter le test si on est en Node.js
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testApiEndpoints();
} else {
  // Browser environment
  console.log('Exécutez testApiEndpoints() dans la console pour tester');
}

module.exports = { testApiEndpoints, getMockData };
