/**
 * Check ObjectId validity
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2023-04-10
 *
 * @param {string} id the object id
 * @returns {RegExpMatchArray | null} true | false
 */
export const checkObjectId = (id: string): RegExpMatchArray | null => {
  return id.match(/^[0-9a-fA-F]{24}$/);
};

/**
 * The isMultipartRequest function checks if a request is a multipart request.
 *
 * <AUTHOR> magde <<EMAIL>>
 * @since 2024-03-22
 *
 * @param {Request} req - The http request.
 * @returns {boolean} True, indicating that the request is a multipart request. Otherwise, the function returns false.
 */
export const isMultipartRequest = function (req: any): boolean {
  const contentTypeHeader = req.headers["content-type"];

  return contentTypeHeader && contentTypeHeader.indexOf("multipart") > -1 || false;
};

// Function to format the error messages
export const formatErrorMessages = function (errorArray: Array<any>): string {
  return errorArray.map(err => `Error: ${err.message}`).join('\n') || "";
}
