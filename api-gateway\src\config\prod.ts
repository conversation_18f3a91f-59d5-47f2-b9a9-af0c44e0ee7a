import dotenv from "dotenv";

dotenv.config();

const prod = {
  // Environment
  env: process.env.NODE_ENV || "production",

  // Server config
  nodeServerPort: process.env.NODE_SERVER_PORT || 2000,
  nodeServerHost: process.env.NODE_SERVER_HOST || "localhost",
  nodeServerPrivateKey: process.env.NODE_SERVER_PRIMARY_KEY?.replace(
    /\\n/g,
    "\n"
  ),
  nodeServerPublicKey: process.env.NODE_SERVER_PUBLIC_KEY?.replace(
    /\\n/g,
    "\n"
  ),

  // Micro services url
  authenticationServiceUrl: process.env.AUTHENTICATION_SERVICE_URL || "https://api.e-luxe.fr",
  blogServiceUrl: process.env.BLOG_SERVICE_URL || "https://blog-service.e-luxe.fr",
  emailServiceUrl: process.env.EMAIL_SERVICE_URL || "https://email-service.e-luxe.frt",
  orderServiceUrl: process.env.ORDER_SERVICE_URL || "https://order-service.e-luxe.fr",
  paymentServiceUrl: process.env.PAYMENT_SERVICE_URL || "https://payment-service.e-luxe.fr",
  productServiceUrl: process.env.PRODUCT_SERVICE_URL || "https://product-service.e-luxe.fr",
  schoolProgramServiceUrl: process.env.SCHOOL_PROGRAM_SERVICE_URL || "https://school-program-service.e-luxe.fr",
  userServiceUrl: process.env.USER_SERVICE_URL || "https://user-service.e-luxe.fr",
  settingServiceUrl: process.env.SETTING_SERVICE_URL || "https://setting-service.e-luxe.fr",

  // Token config
  // One hour for expiration time (time in seconds)
  accessTokenLife: 60 * 60,
  // One day for expiration time (time in seconds)
  refreshTokenLife: 60 * 60 * 24,

  // Redis db
  redisDbPort: process.env.REDIS_DB_PORT || 6379,
  redisDbHost: process.env.REDIS_DB_HOST || "127.0.0.1",
  redisDbUser: process.env.REDIS_DB_USER || "e_luxe",
  redisDbPassword: process.env.REDIS_DB_PASSWORD || "e_luxe2024!",
  redisDbName: process.env.REDIS_DB_NAME || "redis",

  // Mongo db
  mongoDbHost: process.env.MONGODB_DB_HOST || "127.0.0.1",
  mongoDbPort: process.env.MONGODB_DB_PORT || "27017",
  mongoDbUser: process.env.MONGODB_DB_USER || "e_luxe",
  mongoDbPassword: process.env.MONGODB_DB_PASSWORD || "e_luxe2024!",
  mongoDbName: process.env.MONGODB_DB_NAME || "authentication",

  // Swagger documentation
  swaggerBaseUrl: process.env.SWAGGER_BASE_URL || "/v1/docs",
  swaggerAuthBaseUrl: process.env.SWAGGER_AUTH_BASE_URL || "/v1/auth/docs",

  // Serving static files
  userServiceImageBaseUrl: process.env.USER_SERVICE_IMAGE_BASE_URL || "v1/users/images",
};

export default prod;
