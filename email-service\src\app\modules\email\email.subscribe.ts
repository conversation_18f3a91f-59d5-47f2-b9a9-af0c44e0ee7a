import rabbitmqManager from "../../../core/rabbitmq";
import emailService from "./email.service";
import DBManager from "../../../core/db";

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-07-24
 *
 * Class EmailSubscribe
 */
class EmailSubscribe {
  /**
   * Send email
   *
   * <AUTHOR> <<EMAIL>>
   * @since 2023-07-24
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  //   public async sendEmail(): Promise<void> {
  //     try {
  //       const { channel, queue } = await rabbitmqManager.setupQueue(
  //         "eluxe.email.sendMail",
  //         "sendMai",
  //         "sendMailQueue"
  //       );

  //       channel.consume(queue, async (msg: any) => {
  //         try {
  //             console.log("Send Email");
  //           const data = JSON.parse(msg.content);
  //           const dbManager = new DBManager();

  //           // Connect to the database
  //           const dbConnection = await dbManager.asyncOnConnect();

  //           await this.handleSendMail(data, dbConnection);
  //         } catch (error) {
  //           console.error("Message processing failed:", error);
  //         } finally {
  //           channel.ack(msg); // Always acknowledge the message
  //         }
  //       });
  //     } catch (error) {
  //       console.error(error);
  //     }
  //   }

  /**
   * Handles sending an email using the email service.
   *
   * @param {any} data - The email data containing the message to be sent.
   * @param {any} dbConnection - The database connection instance to be closed after processing.
   * @returns {Promise<void>} A promise that resolves when the email is sent and the database connection is closed.
   *
   * @throws {Error} Logs an error message if sending the email fails.
   */
  private async handleSendMail(data: any, dbConnection: any): Promise<void> {
    try {
      console.log(data.messag);
      await emailService.sendEmail(data.message);
    } catch (error) {
      console.error("Error send contact email:", error);
    } finally {
      dbConnection.disconnect();
    }
  }

  /**
   * Send email
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-07-24
   *
   * @return {Promise<void>} the eventual completion or failure
   */
  public async sendEmail(): Promise<void> {
    try {
      const dbManager = new DBManager();
      const exchangeName = "eluxe.email.sendMail";
      const routingKey = "sendMail";
      const queueName = "sendMailQueue";

      await rabbitmqManager.createChannel();

      const channel = rabbitmqManager.channel;

      await channel.assertExchange(exchangeName, "direct");

      const q = await channel.assertQueue(queueName);

      await channel.bindQueue(q.queue, exchangeName, routingKey);

      channel.consume(q.queue, (msg: any) => {
        const data = JSON.parse(msg.content);

        dbManager.asyncOnConnect().then((dbConenction) => {
          emailService
            .sendEmail(data.message)
            .then((result) => {
              if (result === null || result === undefined) {
                // const response = {
                //     status: statusCode.httpNotFound,
                //     errNo: errorNumbers.resourceNotFound,
                //     errMsg: i18n.__("user.profile.userNotFound"),
                // };

                // return customResponse.error(response, res);
                console.error(result);
              } else {
                // const response = {
                //     status: statusCode.httpOk,
                //     data: result,
                // };

                // return customResponse.success(response, res);
                console.log(result);
              }

              // Disconnect from the database
              dbConenction.disconnect();
            })
            .catch((error) => {
              // const response = {
              // status: error?.status || statusCode.httpInternalServerError,
              // errNo: errorNumbers.genericError,
              // errMsg: error?.message || error,
              // };

              // return customResponse.error(response, res);
              console.error(error);
              dbConenction.disconnect();
            });

          channel.ack(msg);
        });
      });
    } catch (error) {
      console.log(error);
    }
  }
}

const emailSubscribe = new EmailSubscribe();
export default emailSubscribe;
