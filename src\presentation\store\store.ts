import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import slices
import authSlice from './slices/authSlice';
import productSlice from './slices/productSlice';
import cartSlice from './slices/cartSlice';
import orderSlice from './slices/orderSlice';
import appSlice from './slices/appSlice';
import settingsSlice from './slices/settingsSlice';
import wishlistSlice from './slices/wishlistSlice';

// Persist config
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth', 'cart', 'app', 'setting'], // Only persist these slices
  blacklist: ['product', 'order'], // Don't persist these slices
};

// Auth persist config
const authPersistConfig = {
  key: 'auth',
  storage: AsyncStorage,
  whitelist: ['user', 'isAuthenticated'], // Only persist user data and auth status
};

// Cart persist config
const cartPersistConfig = {
  key: 'cart',
  storage: AsyncStorage,
};

// App persist config
const appPersistConfig = {
  key: 'app',
  storage: AsyncStorage,
};

// Setting persist config (comme le client web)
const settingPersistConfig = {
  key: 'setting',
  storage: AsyncStorage,
  whitelist: ['globalSetting', 'storeCustomizationSetting'], // Persist settings data
};

// Root reducer
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authSlice),
  product: productSlice,
  cart: persistReducer(cartPersistConfig, cartSlice),
  order: orderSlice,
  app: persistReducer(appPersistConfig, appSlice),
  setting: persistReducer(settingPersistConfig, settingsSlice), // ← "setting" comme le client web
  wishlist: wishlistSlice, // Ajout de la wishlist
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: __DEV__,
});

// Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
