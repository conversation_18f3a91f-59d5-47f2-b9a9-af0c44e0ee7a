import express, { Router } from "express";
import httpProxy from "express-http-proxy";
import routesGrouping from "../utils/routes-grouping.util";
import jwtUtilities from "../utils/jwt-utilities.util";
import statusCode from "../utils/status-code.util";
import errorNumbers from "../utils/error-numbers.util";
import customResponse from "../utils/custom-response.util";
import config from "../../config";

const emailServiceProxy = httpProxy(config.emailServiceUrl, {
  proxyErrorHandler: function (err, res, next) {
    switch (err && err.code) {
      case "ECONNRESET": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      case "ECONNREFUSED": {
        const response = {
          status: err?.status || statusCode.httpInternalServerError,
          errNo: errorNumbers.genericError,
          errMsg: err?.message || JSON.stringify(err),
        };

        return customResponse.error(response, res);
      }
      default: {
        next(err);
      }
    }
  },
});

/**
 * <AUTHOR> Magde <<EMAIL>>
 * @since 2023-07-25
 *
 * Class EmailServiceRoutes
 */
class EmailServiceRoutes {
  private router: Router;

  /**
   * Create a new Routes instance.
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-07-25
   */
  constructor() {
    this.router = express.Router();
  }

  /**
   * Creating all email service routes
   *
   * <AUTHOR> Magde <<EMAIL>>
   * @since 2023-07-25
   *
   * @returns {Router} of email service
   */
  public emailServiceRoutes(): Router {
    return this.router.use(
      routesGrouping.group((router) => {
        // All email routes
        router.use(
          routesGrouping.group((router) => {
            router.use(
              "/email",
              routesGrouping.group((router) => {
                router.post("/send", (req, res, next) => {
                  const bearerToken = jwtUtilities.generateInternToken();

                  // Set request header authorization with generic gateway
                  req.headers.authorization = `Bearer ${bearerToken}`;
                  // Update url with original url which contain all path
                  req.url = req.originalUrl;

                  emailServiceProxy(req, res, next);
                });
              })
            );
          })
        );
      })
    );
  }
}

const emailServiceRoutes = new EmailServiceRoutes();
export default emailServiceRoutes;
