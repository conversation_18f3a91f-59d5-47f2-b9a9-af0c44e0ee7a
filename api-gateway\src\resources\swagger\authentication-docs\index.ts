import config from "../../../config";

export default {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Authentication service documentation",
      summary: "Authentication service documentation",
      baseUrl: "/v1/auth",
      version: "1.0.0",
      description: `This is the documentation of the authentification microservice that gathers all the
      authentication-specific functionalities realized with Express and documented with Swagger.`,
      termsOfService: "#",
      license: {
        name: "Apache 2.0",
        url: "http://www.apache.org/licenses/LICENSE-2.0.html",
      },
      contact: {
        name: "Contact the Developper",
        url: "#",
        email: "<EMAIL>",
      },
    },
    basePath: "/v1/auth/docs",
    externalDocs: {
      description: "Find more info here",
      url: "#",
    },
    servers: [
      {
        url: `${config.authenticationServiceUrl}`,
        description: `${config.env} server`,
        "//variables": {
          username: {
            default: "demo",
            description:
              "this value is assigned by the service provider, in this example `gigantic-server.com`",
          },
          port: {
            enum: ["8443", "443"],
            default: "8443",
          },
          basePath: {
            default: "v2",
          },
        },
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
    },
  },
  apis: [
    "./src/authentication/modules/authentication.route.ts",
    "./src/resources/swagger/authentication-docs/responses/*.yaml",
  ],
};
