/**
 * Service de données mock pour le développement
 * Utilisé comme fallback en cas d'erreur réseau
 */

export class MockDataService {
  
  static getCategories() {
    return {
      status: "SUCCESS",
      data: [
        {
          _id: "1",
          id: "1",
          name: "Electronics",
          slug: "electronics",
          image: "https://via.placeholder.com/150x150/FFD700/000000?text=Electronics",
        },
        {
          _id: "2", 
          id: "2",
          name: "Fashion",
          slug: "fashion",
          image: "https://via.placeholder.com/150x150/C0C0C0/000000?text=Fashion",
        },
        {
          _id: "3",
          id: "3", 
          name: "Home & Garden",
          slug: "home-garden",
          image: "https://via.placeholder.com/150x150/87CEEB/000000?text=Home",
        },
      ]
    };
  }

  static getProducts() {
    return {
      status: "SUCCESS",
      data: {
        products: [
          {
            id: "1",
            _id: "1",
            name: "Luxury Smartphone",
            title: "Luxury Smartphone",
            price: 999.99,
            originalPrice: 1199.99,
            discount: 17,
            image: "https://via.placeholder.com/300x300/FFD700/000000?text=Phone",
            images: ["https://via.placeholder.com/300x300/FFD700/000000?text=Phone"],
            category: { _id: "1", name: "Electronics" },
            rating: 4.5,
            reviewCount: 128,
          },
          {
            id: "2",
            _id: "2", 
            name: "Designer Watch",
            title: "Designer Watch",
            price: 599.99,
            originalPrice: 799.99,
            discount: 25,
            image: "https://via.placeholder.com/300x300/C0C0C0/000000?text=Watch",
            images: ["https://via.placeholder.com/300x300/C0C0C0/000000?text=Watch"],
            category: { _id: "2", name: "Fashion" },
            rating: 4.8,
            reviewCount: 89,
          },
          {
            id: "3",
            _id: "3",
            name: "Smart Home Device", 
            title: "Smart Home Device",
            price: 299.99,
            originalPrice: 399.99,
            discount: 25,
            image: "https://via.placeholder.com/300x300/87CEEB/000000?text=Smart",
            images: ["https://via.placeholder.com/300x300/87CEEB/000000?text=Smart"],
            category: { _id: "3", name: "Home & Garden" },
            rating: 4.3,
            reviewCount: 156,
          },
        ],
        pagination: {
          total: 3,
          page: 1,
          totalPages: 1,
        }
      }
    };
  }

  static getGlobalSettings() {
    return {
      status: "SUCCESS",
      data: {
        shop_name: "E-Luxe Mobile",
        logo: "https://via.placeholder.com/200x60/FFD700/000000?text=E-LUXE",
        currency: "EUR",
        language: "en",
      }
    };
  }

  static getStoreCustomizationSettings() {
    return {
      status: "SUCCESS", 
      data: {
        navbar: {
          logo: "https://via.placeholder.com/200x60/FFD700/000000?text=E-LUXE",
        },
        slider: [
          {
            id: "1",
            title: "Welcome to E-Luxe",
            subtitle: "Luxury Shopping Experience",
            image: "https://via.placeholder.com/800x400/FFD700/000000?text=Slide+1",
            buttonText: "Shop Now",
            buttonLink: "/shop",
          },
          {
            id: "2", 
            title: "Premium Products",
            subtitle: "Quality You Can Trust",
            image: "https://via.placeholder.com/800x400/C0C0C0/000000?text=Slide+2",
            buttonText: "Discover",
            buttonLink: "/products",
          },
        ],
      }
    };
  }

  static getSliderData() {
    return this.getStoreCustomizationSettings().data.slider;
  }

  static getMockDataForUrl(url: string) {
    if (url.includes('/categories')) {
      return this.getCategories();
    } else if (url.includes('/products')) {
      return this.getProducts();
    } else if (url.includes('/setting/global')) {
      return this.getGlobalSettings();
    } else if (url.includes('/setting/store/customization')) {
      return this.getStoreCustomizationSettings();
    } else {
      return {
        status: "SUCCESS",
        data: [],
        message: "Mock data not available for this endpoint"
      };
    }
  }
}

export const mockDataService = new MockDataService();
