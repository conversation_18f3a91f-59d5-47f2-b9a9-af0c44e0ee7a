/**
 * CartSlice PROPRE - Mobile E-Luxe 1.0
 * EXACTEMENT comme le client web - AUCUNE donnée mockée
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../../services/ApiService';

// Interface pour les items du panier
interface CartItem {
  id: string;
  productId: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
  variant?: any;
  total: number;
}

// State interface SIMPLE et PROPRE
interface CartState {
  items: CartItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  discount: number;
  total: number;
  couponCode: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: CartState = {
  items: [],
  subtotal: 0,
  shipping: 0,
  tax: 0,
  discount: 0,
  total: 0,
  couponCode: null,
  isLoading: false,
  error: null,
};

// ===== ACTIONS ASYNC PROPRES =====

/**
 * Récupère le panier depuis l'API
 */
export const getCartAsync = createAsyncThunk(
  'cart/getCart',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.getCart();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cart');
    }
  }
);

/**
 * Ajoute un produit au panier via l'API
 */
export const addToCartAsync = createAsyncThunk(
  'cart/addToCart',
  async (item: { productId: string; quantity: number; variantId?: string }, { rejectWithValue }) => {
    try {
      const response = await apiService.addToCart(item.productId, item.quantity, item.variantId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add to cart');
    }
  }
);

// ===== SLICE PROPRE =====

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    // Actions locales (pour fonctionnement offline)
    addToCartLocal: (state, action: PayloadAction<Omit<CartItem, 'total'>>) => {
      const existingItem = state.items.find(item => 
        item.productId === action.payload.productId && 
        JSON.stringify(item.variant) === JSON.stringify(action.payload.variant)
      );

      if (existingItem) {
        existingItem.quantity += action.payload.quantity;
        existingItem.total = existingItem.price * existingItem.quantity;
      } else {
        const newItem: CartItem = {
          ...action.payload,
          total: action.payload.price * action.payload.quantity,
        };
        state.items.push(newItem);
      }

      cartSlice.caseReducers.calculateTotals(state);
    },

    updateCartItemLocal: (state, action: PayloadAction<{ itemId: string; quantity: number }>) => {
      const item = state.items.find(item => item.id === action.payload.itemId);
      if (item) {
        item.quantity = action.payload.quantity;
        item.total = item.price * item.quantity;
        cartSlice.caseReducers.calculateTotals(state);
      }
    },

    removeFromCartLocal: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      cartSlice.caseReducers.calculateTotals(state);
    },

    clearCartLocal: (state) => {
      state.items = [];
      state.subtotal = 0;
      state.shipping = 0;
      state.tax = 0;
      state.discount = 0;
      state.total = 0;
      state.couponCode = null;
    },

    applyCoupon: (state, action: PayloadAction<{ code: string; discount: number }>) => {
      state.couponCode = action.payload.code;
      state.discount = action.payload.discount;
      cartSlice.caseReducers.calculateTotals(state);
    },

    removeCoupon: (state) => {
      state.couponCode = null;
      state.discount = 0;
      cartSlice.caseReducers.calculateTotals(state);
    },

    calculateTotals: (state) => {
      state.subtotal = state.items.reduce((sum, item) => sum + item.total, 0);
      state.tax = state.subtotal * 0.1; // 10% tax
      state.shipping = state.subtotal > 100 ? 0 : 10; // Free shipping over $100
      state.total = state.subtotal + state.tax + state.shipping - state.discount;
    },
  },
  extraReducers: (builder) => {
    // Get cart
    builder
      .addCase(getCartAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCartAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload.items || [];
        state.subtotal = action.payload.subtotal || 0;
        state.shipping = action.payload.shipping || 0;
        state.tax = action.payload.tax || 0;
        state.discount = action.payload.discount || 0;
        state.total = action.payload.total || 0;
        state.couponCode = action.payload.couponCode || null;
      })
      .addCase(getCartAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Add to cart
    builder
      .addCase(addToCartAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addToCartAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        // Refresh cart data from API response
        if (action.payload.items) {
          state.items = action.payload.items;
          cartSlice.caseReducers.calculateTotals(state);
        }
      })
      .addCase(addToCartAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addToCartLocal,
  updateCartItemLocal,
  removeFromCartLocal,
  clearCartLocal,
  applyCoupon,
  removeCoupon,
  calculateTotals,
} = cartSlice.actions;

export default cartSlice.reducer;
