/**
 * LanguageSwitcher - Mobile E-Luxe 1.0
 * Sélecteur de langue fonctionnel avec libellés corrects
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';
import { ELuxeColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

const LANGUAGES: Language[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
  },
];

interface LanguageSwitcherProps {
  style?: any;
  showLabel?: boolean;
  compact?: boolean;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  style,
  showLabel = true,
  compact = false,
}) => {
  const { i18n } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const currentLanguage = LANGUAGES.find(lang => lang.code === i18n.language) || LANGUAGES[0];

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await i18n.changeLanguage(languageCode);
      setIsModalVisible(false);
      
      // Log pour debug
      console.log('🌐 Langue changée vers:', languageCode);
    } catch (error) {
      console.error('❌ Erreur lors du changement de langue:', error);
    }
  };

  const renderLanguageOption = (language: Language) => (
    <TouchableOpacity
      key={language.code}
      style={[
        styles.languageOption,
        currentLanguage.code === language.code && styles.selectedLanguageOption,
      ]}
      onPress={() => handleLanguageChange(language.code)}
    >
      <Text style={styles.languageFlag}>{language.flag}</Text>
      <View style={styles.languageInfo}>
        <Text style={[
          styles.languageName,
          currentLanguage.code === language.code && styles.selectedLanguageName,
        ]}>
          {language.name}
        </Text>
        <Text style={[
          styles.languageNativeName,
          currentLanguage.code === language.code && styles.selectedLanguageNativeName,
        ]}>
          {language.nativeName}
        </Text>
      </View>
      {currentLanguage.code === language.code && (
        <Icon name="check" size={20} color={ELuxeColors.primary} />
      )}
    </TouchableOpacity>
  );

  if (compact) {
    return (
      <TouchableOpacity
        style={[styles.compactButton, style]}
        onPress={() => setIsModalVisible(true)}
      >
        <Text style={styles.compactFlag}>{currentLanguage.flag}</Text>
        <Icon name="keyboard-arrow-down" size={16} color={ELuxeColors.textSecondary} />
        
        <Modal
          visible={isModalVisible}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setIsModalVisible(false)}
        >
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Language</Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <Icon name="close" size={24} color={ELuxeColors.textPrimary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.languageList}>
              {LANGUAGES.map(renderLanguageOption)}
            </View>
          </SafeAreaView>
        </Modal>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.languageButton, style]}
      onPress={() => setIsModalVisible(true)}
    >
      <Text style={styles.languageFlag}>{currentLanguage.flag}</Text>
      {showLabel && (
        <Text style={styles.languageLabel}>{currentLanguage.code.toUpperCase()}</Text>
      )}
      <Icon name="keyboard-arrow-down" size={16} color={ELuxeColors.textSecondary} />
      
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Language</Text>
            <TouchableOpacity onPress={() => setIsModalVisible(false)}>
              <Icon name="close" size={24} color={ELuxeColors.textPrimary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.languageList}>
            {LANGUAGES.map(renderLanguageOption)}
          </View>
        </SafeAreaView>
      </Modal>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: ELuxeColors.grey2,
    gap: 6,
  },
  compactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 4,
    gap: 4,
  },
  languageFlag: {
    fontSize: 16,
  },
  compactFlag: {
    fontSize: 14,
  },
  languageLabel: {
    ...TextStyles.caption,
    color: ELuxeColors.textPrimary,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: ELuxeColors.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
  },
  modalTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textPrimary,
  },
  languageList: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 28, // Énorme padding pour visibilité maximale
    paddingHorizontal: 28, // Énorme padding horizontal
    borderRadius: 16, // Plus arrondi
    marginBottom: 16, // Plus d'espacement
    backgroundColor: ELuxeColors.white, // Fond blanc pour contraste maximal
    gap: 20, // Plus d'espace entre les éléments
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 4 }, // Ombre plus forte
    shadowOpacity: 0.2, // Ombre plus visible
    shadowRadius: 8, // Ombre plus diffuse
    elevation: 6, // Élévation plus forte
    borderWidth: 2, // Bordure plus épaisse
    borderColor: ELuxeColors.border1,
  },
  selectedLanguageOption: {
    backgroundColor: ELuxeColors.primaryLight,
    borderWidth: 2, // Bordure plus épaisse
    borderColor: ELuxeColors.primary,
    shadowOpacity: 0.2, // Ombre plus visible
    elevation: 5,
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
    fontWeight: '700', // Plus gras pour meilleure visibilité
    marginBottom: 4,
    fontSize: 16, // Plus grand
  },
  selectedLanguageName: {
    color: ELuxeColors.primary,
    fontWeight: '800', // Encore plus gras pour l'option sélectionnée
  },
  languageNativeName: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
    fontSize: 14, // Plus grand pour meilleure lisibilité
  },
  selectedLanguageNativeName: {
    color: ELuxeColors.primary,
  },
});

export default LanguageSwitcher;
