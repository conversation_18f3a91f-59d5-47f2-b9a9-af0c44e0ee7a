# This is a basic workflow to help you get started with Actions
name: Release Server Kitecole Setting Service

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ "main" ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
    - name: 'Create env file'
      run: |
        echo "${{ secrets.DOT_ENV_PRODUCTION }}" > .env
  
    - name: Copy .env file to server using SCP
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        source: ".env"
        target: ${{ secrets.SERVER_PATH }}
  
    - name: Deploy using ssh
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: 22
        script: |
          cd /var/www/servers/server-e-luxe/server-e-luxe-setting-service
          # git reset --hard HEAD
          git clean -fd
          git pull origin main
          npm install
          npm run build
          pm2 restart e-luxe-setting-service
